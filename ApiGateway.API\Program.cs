using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.OpenApi.Models;
using Ocelot.DependencyInjection;
using Ocelot.Middleware;
using System.Collections.Generic;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllers();

// Configure Ocelot
builder.Configuration.AddJsonFile("ocelot.json", optional: false, reloadOnChange: true);
builder.Services.AddOcelot(builder.Configuration);

// Configure Swagger
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("branch", new OpenApiInfo { Title = "Branch Management API", Version = "v1" });
    c.SwaggerDoc("identity", new OpenApiInfo { Title = "Identity API", Version = "v1" });
    c.SwaggerDoc("subscription", new OpenApiInfo { Title = "Subscription API", Version = "v1" });
    c.SwaggerDoc("tenant", new OpenApiInfo { Title = "Tenant Management API", Version = "v1" });
    c.SwaggerDoc("vehicle", new OpenApiInfo { Title = "Vehicle Management API", Version = "v1" });

    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });

    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            new string[] {}
        }
    });
});

// Configure CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll",
        builder =>
        {
            builder.AllowAnyOrigin()
                   .AllowAnyMethod()
                   .AllowAnyHeader();
        });
});

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/branch/swagger.json", "Branch Management API");
        c.SwaggerEndpoint("/swagger/identity/swagger.json", "Identity API");
        c.SwaggerEndpoint("/swagger/subscription/swagger.json", "Subscription API");
        c.SwaggerEndpoint("/swagger/tenant/swagger.json", "Tenant Management API");
        c.SwaggerEndpoint("/swagger/vehicle/swagger.json", "Vehicle Management API");
    });
}

app.UseHttpsRedirection();
app.UseCors("AllowAll");
app.UseAuthorization();

await app.UseOcelot();

app.Run(); 