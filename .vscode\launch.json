{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "compounds": [
        {
            "name": "Run All Services",
            "configurations": [
                "IdentityService.API",
                "SubscriptionService.API",
                "TenantManagementService.API",
                "BranchManagementService.API",
                "VehicleManagementService.API"
            ],
            "stopAll": true
        }
    ],
    "configurations": [
        {
            "name": "IdentityService.API",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build-identity",
            "program": "${workspaceFolder}/IdentityService/src/IdentityService.API/bin/Debug/net8.0/IdentityService.API.dll",
            "args": [],
            "cwd": "${workspaceFolder}/IdentityService/src/IdentityService.API",
            "stopAtEntry": false,
            "serverReadyAction": {
                "action": "openExternally",
                "pattern": "\\bNow listening on:\\s+(https?://\\S+)",
                "uriFormat": "%s/swagger"
            },
            "env": {
                "ASPNETCORE_ENVIRONMENT": "Development"
            },
            "sourceFileMap": {
                "/Views": "${workspaceFolder}/Views"
            }
        },
        {
            "name": "SubscriptionService.API",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build-subscription",
            "program": "${workspaceFolder}/SubscriptionService/src/SubscriptionService.API/bin/Debug/net8.0/SubscriptionService.API.dll",
            "args": [],
            "cwd": "${workspaceFolder}/SubscriptionService/src/SubscriptionService.API",
            "stopAtEntry": false,
            "serverReadyAction": {
                "action": "openExternally",
                "pattern": "\\bNow listening on:\\s+(https?://\\S+)",
                "uriFormat": "%s/swagger"
            },
            "env": {
                "ASPNETCORE_ENVIRONMENT": "Development"
            },
            "sourceFileMap": {
                "/Views": "${workspaceFolder}/Views"
            }
        },
        {
            "name": "TenantManagementService.API",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build-tenant",
            "program": "${workspaceFolder}/TenantManagementService/src/TenantManagementService.API/bin/Debug/net8.0/TenantManagementService.API.dll",
            "args": [],
            "cwd": "${workspaceFolder}/TenantManagementService/src/TenantManagementService.API",
            "stopAtEntry": false,
            "serverReadyAction": {
                "action": "openExternally",
                "pattern": "\\bNow listening on:\\s+(https?://\\S+)",
                "uriFormat": "%s/swagger"
            },
            "env": {
                "ASPNETCORE_ENVIRONMENT": "Development"
            },
            "sourceFileMap": {
                "/Views": "${workspaceFolder}/Views"
            }
        },
        {
            "name": "BranchManagementService.API",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build-branch",
            "program": "${workspaceFolder}/BranchManagementService/src/BranchManagementService.API/bin/Debug/net8.0/BranchManagementService.API.dll",
            "args": [],
            "cwd": "${workspaceFolder}/BranchManagementService/src/BranchManagementService.API",
            "stopAtEntry": false,
            "serverReadyAction": {
                "action": "openExternally",
                "pattern": "\\bNow listening on:\\s+(https?://\\S+)",
                "uriFormat": "%s/swagger"
            },
            "env": {
                "ASPNETCORE_ENVIRONMENT": "Development"
            },
            "sourceFileMap": {
                "/Views": "${workspaceFolder}/Views"
            }
        },
        {
            "name": "VehicleManagementService.API",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build-vehicle",
            "program": "${workspaceFolder}/VehicleManagementService/src/VehicleManagementService.API/bin/Debug/net8.0/VehicleManagementService.API.dll",
            "args": [],
            "cwd": "${workspaceFolder}/VehicleManagementService/src/VehicleManagementService.API",
            "stopAtEntry": false,
            "serverReadyAction": {
                "action": "openExternally",
                "pattern": "\\bNow listening on:\\s+(https?://\\S+)",
                "uriFormat": "%s/swagger"
            },
            "env": {
                "ASPNETCORE_ENVIRONMENT": "Development"
            },
            "sourceFileMap": {
                "/Views": "${workspaceFolder}/Views"
            }
        },
        {
            "name": "VMSContracts (Build Only)",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build-contracts",
            "program": "dotnet",
            "args": ["--info"],
            "cwd": "${workspaceFolder}/VMSContracts",
            "stopAtEntry": false
        }
    ]
}