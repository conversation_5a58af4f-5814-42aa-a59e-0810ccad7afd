using System;
using System.Threading.Tasks;
using IdentityService.Domain.Interfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace IdentityService.API.Middleware
{
    public class UserIdPropagationMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<UserIdPropagationMiddleware> _logger;

        public UserIdPropagationMiddleware(RequestDelegate next, ILogger<UserIdPropagationMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context, ICurrentUserService currentUserService)
        {
            try
            {
                // Log the current user ID from the token
                var userId = currentUserService.UserId;
                if (userId.HasValue)
                {
                    _logger.LogInformation("User ID from token: {UserId}", userId.Value);
                    
                    // Add the user ID to the HttpContext.Items dictionary
                    // This will make it available throughout the request pipeline
                    context.Items["UserId"] = userId.Value;
                }
                else
                {
                    _logger.LogWarning("No user ID found in token");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in UserIdPropagationMiddleware");
            }

            // Call the next middleware in the pipeline
            await _next(context);
        }
    }
}
