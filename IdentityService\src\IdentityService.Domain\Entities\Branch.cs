using System;
using System.Collections.Generic;
using IdentityService.Domain.Common;

namespace IdentityService.Domain.Entities;

public class Branch : BaseEntity
{
    public string Name { get; private set; }
    public string Code { get; private set; }
    public string Address { get; private set; }
    public string ContactNumber { get; private set; }
    public string Email { get; private set; }
    public bool IsActive { get; private set; }
    public List<UserBranch> UserBranches { get; private set; }

    private Branch() { }

    public static Branch Create(
        string name,
        string code,
        string address,
        string contactNumber,
        string email,
        string createdBy)
    {
        return new Branch
        {
            Name = name,
            Code = code,
            Address = address,
            ContactNumber = contactNumber,
            Email = email,
            IsActive = true,
            CreatedBy = createdBy,
            UserBranches = new List<UserBranch>()
        };
    }

    public void Update(
        string name,
        string code,
        string address,
        string contactNumber,
        string email,
        string updatedBy)
    {
        Name = name;
        Code = code;
        Address = address;
        ContactNumber = contactNumber;
        Email = email;
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;
    }

    public void Activate(string updatedBy)
    {
        IsActive = true;
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;
    }

    public void Deactivate(string updatedBy)
    {
        IsActive = false;
        UpdatedBy = updatedBy;
        UpdatedAt = DateTime.UtcNow;
    }
} 