FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["src/BranchManagementService.API/BranchManagementService.API.csproj", "BranchManagementService.API/"]
COPY ["src/BranchManagementService.Application/BranchManagementService.Application.csproj", "BranchManagementService.Application/"]
COPY ["src/BranchManagementService.Domain/BranchManagementService.Domain.csproj", "BranchManagementService.Domain/"]
COPY ["src/BranchManagementService.Infrastructure/BranchManagementService.Infrastructure.csproj", "BranchManagementService.Infrastructure/"]
COPY ["src/BranchManagementService.Shared/BranchManagementService.Shared.csproj", "BranchManagementService.Shared/"]
COPY ["VMSContracts/VMSContracts.csproj", "VMSContracts/"]
RUN dotnet restore "BranchManagementService.API/BranchManagementService.API.csproj"
COPY src/ .
COPY VMSContracts/ VMSContracts/
WORKDIR "/src/BranchManagementService.API"
RUN dotnet build "BranchManagementService.API.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "BranchManagementService.API.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "BranchManagementService.API.dll"]
