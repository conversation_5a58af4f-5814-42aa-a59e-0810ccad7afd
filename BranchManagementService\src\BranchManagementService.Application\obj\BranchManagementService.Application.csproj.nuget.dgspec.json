{"format": 1, "restore": {"D:\\Projects\\VehicleManagementSystem\\VMS-API\\BranchManagementService\\src\\BranchManagementService.Application\\BranchManagementService.Application.csproj": {}}, "projects": {"D:\\Projects\\VehicleManagementSystem\\VMS-API\\BranchManagementService\\src\\BranchManagementService.Application\\BranchManagementService.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Projects\\VehicleManagementSystem\\VMS-API\\BranchManagementService\\src\\BranchManagementService.Application\\BranchManagementService.Application.csproj", "projectName": "BranchManagementService.Application", "projectPath": "D:\\Projects\\VehicleManagementSystem\\VMS-API\\BranchManagementService\\src\\BranchManagementService.Application\\BranchManagementService.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Projects\\VehicleManagementSystem\\VMS-API\\BranchManagementService\\src\\BranchManagementService.Application\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\Projects\\VehicleManagementSystem\\VMS-API\\BranchManagementService\\src\\BranchManagementService.Domain\\BranchManagementService.Domain.csproj": {"projectPath": "D:\\Projects\\VehicleManagementSystem\\VMS-API\\BranchManagementService\\src\\BranchManagementService.Domain\\BranchManagementService.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[12.0.1, )"}, "AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[12.0.1, )"}, "FluentValidation": {"target": "Package", "version": "[11.9.0, )"}, "FluentValidation.DependencyInjectionExtensions": {"target": "Package", "version": "[11.9.0, )"}, "MediatR": {"target": "Package", "version": "[11.1.0, )"}, "MediatR.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[11.1.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.201/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Projects\\VehicleManagementSystem\\VMS-API\\BranchManagementService\\src\\BranchManagementService.Domain\\BranchManagementService.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Projects\\VehicleManagementSystem\\VMS-API\\BranchManagementService\\src\\BranchManagementService.Domain\\BranchManagementService.Domain.csproj", "projectName": "BranchManagementService.Domain", "projectPath": "D:\\Projects\\VehicleManagementSystem\\VMS-API\\BranchManagementService\\src\\BranchManagementService.Domain\\BranchManagementService.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Projects\\VehicleManagementSystem\\VMS-API\\BranchManagementService\\src\\BranchManagementService.Domain\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\Projects\\VehicleManagementSystem\\VMS-API\\VMSContracts\\VMSContracts.csproj": {"projectPath": "D:\\Projects\\VehicleManagementSystem\\VMS-API\\VMSContracts\\VMSContracts.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.201/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Projects\\VehicleManagementSystem\\VMS-API\\VMSContracts\\VMSContracts.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Projects\\VehicleManagementSystem\\VMS-API\\VMSContracts\\VMSContracts.csproj", "projectName": "VMSContracts", "projectPath": "D:\\Projects\\VehicleManagementSystem\\VMS-API\\VMSContracts\\VMSContracts.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Projects\\VehicleManagementSystem\\VMS-API\\VMSContracts\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"MassTransit": {"target": "Package", "version": "[8.1.3, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Http.Polly": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.201/PortableRuntimeIdentifierGraph.json"}}}}}