using System;
using System.Threading;
using System.Threading.Tasks;
using IdentityService.Application.Common;
using IdentityService.Application.DTOs.Roles;
using IdentityService.Application.Interfaces;
using IdentityService.Domain.Entities;
using IdentityService.Domain.Interfaces;
using MediatR;

namespace IdentityService.Application.Features.Roles.Commands;

public class CreateRoleCommand : BaseRequest<RoleResponse>
{
    public required CreateRoleRequest Request { get; set; }
}

public class CreateRoleCommandHandler : IRequestHandler<CreateRoleCommand, RoleResponse>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICurrentUserService _currentUserService;
    private readonly IAuditLogService _auditLogService;

    public CreateRoleCommandHandler(
        IUnitOfWork unitOfWork,
        ICurrentUserService currentUserService,
        IAuditLogService auditLogService)
    {
        _unitOfWork = unitOfWork;
        _currentUserService = currentUserService;
        _auditLogService = auditLogService;
    }

    public async Task<RoleResponse> Handle(CreateRoleCommand command, CancellationToken cancellationToken)
    {
        var request = command.Request;
        var createdBy = _currentUserService.UserId.ToString() ?? "System";

        // Create role
        var role = Role.Create(request.Name, request.Description, createdBy);

        // Assign permissions if provided
        if (request.PermissionIds != null && request.PermissionIds.Count > 0)
        {
            foreach (var permissionId in request.PermissionIds)
            {
                var permission = await _unitOfWork.PermissionRepository.GetByIdAsync(permissionId);
                if (permission != null)
                {
                    role.AddPermission(permission, createdBy);
                }
            }
        }

        // Save role
        await _unitOfWork.RoleRepository.AddAsync(role);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        // Create audit log
        // await _auditLogService.CreateAuditLogAsync(
        //     "Create",
        //     "Role",
        //     role.Id.ToString(),
        //     string.Empty,
        //     System.Text.Json.JsonSerializer.Serialize(new { role.Id, role.Name, role.Description }),
        //     "Id,Name,Description",
        //     _currentUserService.UserId ?? Guid.Empty);

        // Get permissions for response
        var permissions = new List<RolePermissionResponse>();
        foreach (var rp in role.RolePermissions)
        {
            var permission = await _unitOfWork.PermissionRepository.GetByIdAsync(rp.PermissionId);
            if (permission != null)
            {
                permissions.Add(new RolePermissionResponse
                {
                    Id = permission.Id,
                    Name = permission.Name,
                    Description = permission.Description,
                    Resource = permission.Resource,
                    Action = permission.Action
                });
            }
        }

        // Create response
        var response = new RoleResponse
        {
            Id = role.Id,
            Name = role.Name,
            Description = role.Description,
            CreatedAt = role.CreatedAt,
            CreatedBy = role.CreatedBy,
            UpdatedAt = role.UpdatedAt,
            UpdatedBy = role.UpdatedBy,
            Permissions = permissions
        };

        return response;
    }
}
