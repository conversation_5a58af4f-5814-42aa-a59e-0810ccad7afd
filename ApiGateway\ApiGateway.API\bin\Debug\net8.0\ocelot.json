{"Routes": [{"DownstreamPathTemplate": "/swagger/v1/swagger.json", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 5001}], "UpstreamPathTemplate": "/swagger/branch/swagger.json", "UpstreamHttpMethod": ["GET"]}, {"DownstreamPathTemplate": "/swagger/v1/swagger.json", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 5002}], "UpstreamPathTemplate": "/swagger/identity/swagger.json", "UpstreamHttpMethod": ["GET"]}, {"DownstreamPathTemplate": "/swagger/v1/swagger.json", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 5003}], "UpstreamPathTemplate": "/swagger/subscription/swagger.json", "UpstreamHttpMethod": ["GET"]}, {"DownstreamPathTemplate": "/swagger/v1/swagger.json", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 5004}], "UpstreamPathTemplate": "/swagger/tenant/swagger.json", "UpstreamHttpMethod": ["GET"]}, {"DownstreamPathTemplate": "/swagger/v1/swagger.json", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 5005}], "UpstreamPathTemplate": "/swagger/vehicle/swagger.json", "UpstreamHttpMethod": ["GET"]}, {"DownstreamPathTemplate": "/api/{everything}", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 5001}], "UpstreamPathTemplate": "/branch/{everything}", "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE"]}, {"DownstreamPathTemplate": "/api/{everything}", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 5002}], "UpstreamPathTemplate": "/identity/{everything}", "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE"]}, {"DownstreamPathTemplate": "/api/{everything}", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 5003}], "UpstreamPathTemplate": "/subscription/{everything}", "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE"]}, {"DownstreamPathTemplate": "/api/{everything}", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 5004}], "UpstreamPathTemplate": "/tenant/{everything}", "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE"]}, {"DownstreamPathTemplate": "/api/{everything}", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 5005}], "UpstreamPathTemplate": "/vehicle/{everything}", "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE"]}], "GlobalConfiguration": {"BaseUrl": "http://localhost:5000"}}