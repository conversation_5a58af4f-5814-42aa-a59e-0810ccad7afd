using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using IdentityService.Application.DTOs.Subscriptions;
using IdentityService.Application.Features.Subscriptions.Commands;
using IdentityService.Application.Features.Subscriptions.Queries;
using IdentityService.Application.Interfaces;
using IdentityService.Domain.Interfaces;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace IdentityService.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class SubscriptionsController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ICurrentUserService _currentUserService;
    private readonly ILogger<SubscriptionsController> _logger;

    public SubscriptionsController(
        IMediator mediator,
        ICurrentUserService currentUserService,
        ILogger<SubscriptionsController> logger)
    {
        _mediator = mediator;
        _currentUserService = currentUserService;
        _logger = logger;
    }

    [HttpGet]
    public async Task<ActionResult<List<UserSubscriptionResponse>>> GetUserSubscriptions()
    {
        try
        {
            var userId = _currentUserService.UserId;
            if (!userId.HasValue)
                return Unauthorized();

            var query = new GetUserSubscriptionsQuery { UserId = userId.Value };
            var subscriptions = await _mediator.Send(query);
            return Ok(subscriptions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving user subscriptions");
            return StatusCode(500, new { message = "An error occurred while retrieving subscriptions" });
        }
    }

    [HttpGet("active")]
    public async Task<ActionResult<UserSubscriptionResponse>> GetActiveSubscription()
    {
        try
        {
            var userId = _currentUserService.UserId;
            if (!userId.HasValue)
                return Unauthorized();

            var query = new GetActiveSubscriptionQuery { UserId = userId.Value };
            var subscription = await _mediator.Send(query);

            if (subscription == null)
                return NotFound(new { message = "No active subscription found" });

            return Ok(subscription);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving active subscription");
            return StatusCode(500, new { message = "An error occurred while retrieving the active subscription" });
        }
    }

    [HttpGet("features")]
    public async Task<ActionResult<Dictionary<string, bool>>> GetUserFeatures()
    {
        try
        {
            var userId = _currentUserService.UserId;
            if (!userId.HasValue)
                return Unauthorized();

            var query = new GetActiveSubscriptionQuery { UserId = userId.Value };
            var subscription = await _mediator.Send(query);

            if (subscription == null)
                return Ok(new Dictionary<string, bool>());

            var features = subscription.Features
                .ToDictionary(f => f.FeatureName, f => f.IsEnabled);

            return Ok(features);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving user features");
            return StatusCode(500, new { message = "An error occurred while retrieving user features" });
        }
    }

    [HttpGet("tier")]
    public async Task<ActionResult<string>> GetUserSubscriptionTier()
    {
        try
        {
            var userId = _currentUserService.UserId;
            if (!userId.HasValue)
                return Unauthorized();

            var tier = await _mediator.Send(new GetUserSubscriptionTierQuery { UserId = userId.Value });
            return Ok(tier.ToString());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving user subscription tier");
            return StatusCode(500, new { message = "An error occurred while retrieving subscription tier" });
        }
    }

    [HttpPost("{id}/features")]
    [Authorize(Policy = "Subscriptions.Manage")]
    public async Task<ActionResult<UserSubscriptionResponse>> AddSubscriptionFeature(Guid id, [FromBody] AddSubscriptionFeatureRequest request)
    {
        try
        {
            var command = new AddSubscriptionFeatureCommand
            {
                SubscriptionId = id,
                Request = request
            };
            var subscription = await _mediator.Send(command);
            return Ok(subscription);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation during feature addition");
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding subscription feature");
            return StatusCode(500, new { message = "An error occurred while adding the subscription feature" });
        }
    }

    [HttpPut("{id}/features/{featureName}")]
    [Authorize(Policy = "Subscriptions.Manage")]
    public async Task<ActionResult<UserSubscriptionResponse>> UpdateSubscriptionFeature(
        Guid id,
        string featureName,
        [FromBody] UpdateSubscriptionFeatureRequest request)
    {
        try
        {
            var command = new UpdateSubscriptionFeatureCommand
            {
                SubscriptionId = id,
                FeatureName = featureName,
                Request = request
            };
            var subscription = await _mediator.Send(command);
            return Ok(subscription);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation during feature update");
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating subscription feature");
            return StatusCode(500, new { message = "An error occurred while updating the subscription feature" });
        }
    }

    [HttpDelete("{id}/features/{featureName}")]
    [Authorize(Policy = "Subscriptions.Manage")]
    public async Task<ActionResult<UserSubscriptionResponse>> RemoveSubscriptionFeature(Guid id, string featureName)
    {
        try
        {
            var command = new RemoveSubscriptionFeatureCommand
            {
                SubscriptionId = id,
                FeatureName = featureName
            };
            var subscription = await _mediator.Send(command);
            return Ok(subscription);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation during feature removal");
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing subscription feature");
            return StatusCode(500, new { message = "An error occurred while removing the subscription feature" });
        }
    }
}
