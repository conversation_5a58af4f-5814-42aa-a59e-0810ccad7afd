using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using IdentityService.Application.Common;
using IdentityService.Application.DTOs.Roles;
using IdentityService.Domain.Interfaces;
using MediatR;

namespace IdentityService.Application.Features.Roles.Queries;

public class GetRoleByIdQuery : BaseRequest<RoleResponse>
{
    public Guid RoleId { get; set; }
}

public class GetRoleByIdQueryHandler : IRequestHandler<GetRoleByIdQuery, RoleResponse>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetRoleByIdQueryHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    public async Task<RoleResponse> Handle(GetRoleByIdQuery query, CancellationToken cancellationToken)
    {
        var role = await _unitOfWork.RoleRepository.GetByIdAsync(query.RoleId);
        if (role == null)
            throw new InvalidOperationException($"Role with ID {query.RoleId} not found");

        var permissions = new List<RolePermissionResponse>();
        foreach (var rp in role.RolePermissions)
        {
            var permission = await _unitOfWork.PermissionRepository.GetByIdAsync(rp.PermissionId);
            if (permission != null)
            {
                permissions.Add(new RolePermissionResponse
                {
                    Id = permission.Id,
                    Name = permission.Name,
                    Description = permission.Description,
                    Resource = permission.Resource,
                    Action = permission.Action
                });
            }
        }

        return new RoleResponse
        {
            Id = role.Id,
            Name = role.Name,
            Description = role.Description,
            CreatedAt = role.CreatedAt,
            CreatedBy = role.CreatedBy,
            UpdatedAt = role.UpdatedAt,
            UpdatedBy = role.UpdatedBy,
            Permissions = permissions
        };
    }
}
