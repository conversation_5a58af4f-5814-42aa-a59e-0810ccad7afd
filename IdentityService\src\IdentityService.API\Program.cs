using System.Text;
using Microsoft.OpenApi.Models;
using IdentityService.Domain.Interfaces;
using IdentityService.Application.Interfaces;
using IdentityService.Application.Features.Auth.Commands;
using IdentityService.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Serilog;
using Serilog.Events;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using MediatR;
using AutoMapper;
using IdentityService.Infrastructure.Persistence.Repositories;
using System.Security.Claims;
using Microsoft.AspNetCore.Http;
using IdentityService.Infrastructure.Services;
using IdentityService.Infrastructure.Messaging;
using Microsoft.AspNetCore.Rewrite;
using IdentityService.Domain.Enums;
using IdentityService.Infrastructure.Authorization;
using Microsoft.AspNetCore.Authorization;
using FluentValidation;
using IdentityService.Application.Behaviors;
using IdentityService.API.Middleware;
using IdentityService.Application.Validators;
using IdentityService.API.Filters;
using IdentityService.Application.Common;
using IdentityService.Application.Interfaces;
using VMS.Contracts.ServiceClients;
using Microsoft.AspNetCore.Rewrite;

var builder = WebApplication.CreateBuilder(args);

// Configure Serilog
Log.Logger = new LoggerConfiguration()
    .MinimumLevel.Debug()
    .MinimumLevel.Override("Microsoft", LogEventLevel.Information)
    .Enrich.FromLogContext()
    .WriteTo.Console()
    .WriteTo.File("logs/identity-service-.txt", rollingInterval: RollingInterval.Day)
    .CreateLogger();

builder.Host.UseSerilog();

// Add services to the container
builder.Services.AddControllers();

// Configure DbContext
builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseNpgsql(builder.Configuration.GetConnectionString("DefaultConnection")));

// Register IHttpContextAccessor
builder.Services.AddHttpContextAccessor();

// Configure JWT Authentication
builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuer = true,
        ValidateAudience = true,
        ValidateLifetime = true,
        ValidateIssuerSigningKey = true,
        ValidIssuer = builder.Configuration["Jwt:Issuer"],
        ValidAudience = builder.Configuration["Jwt:Audience"],
        IssuerSigningKey = new SymmetricSecurityKey(
            Encoding.UTF8.GetBytes(builder.Configuration["Jwt:Key"] ?? throw new InvalidOperationException("JWT Key is not configured"))),
        ClockSkew = TimeSpan.Zero, // Remove delay of token when expire
        // Don't set NameClaimType and RoleClaimType to allow both short and full URI formats
        // The CurrentUserService will handle both formats
    };

    // Add event handlers for debugging
    options.Events = new JwtBearerEvents
    {
        OnAuthenticationFailed = context =>
        {
            var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
            logger.LogError("Authentication failed: {Exception}", context.Exception);
            return Task.CompletedTask;
        },
        OnTokenValidated = context =>
        {
            var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();

            // Log all claims for debugging
            var claims = context.Principal?.Claims.Select(c => $"{c.Type}: {c.Value}").ToList();
            if (claims != null && claims.Any())
            {
                logger.LogInformation("Token validated successfully with claims: {Claims}", string.Join(", ", claims));
            }
            else
            {
                logger.LogWarning("Token validated but no claims found");
            }

            // Ensure the NameIdentifier claim exists
            var nameIdentifierClaim = context.Principal?.FindFirst(ClaimTypes.NameIdentifier);
            if (nameIdentifierClaim == null)
            {
                logger.LogWarning("NameIdentifier claim not found in token");
            }
            else
            {
                logger.LogInformation("NameIdentifier claim found: {Value}", nameIdentifierClaim.Value);
            }

            return Task.CompletedTask;
        },
        OnChallenge = context =>
        {
            var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
            logger.LogWarning("OnChallenge: {Error}", context.Error);
            return Task.CompletedTask;
        },
        OnMessageReceived = context =>
        {
            var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
            logger.LogInformation("Token received and is being processed");
            return Task.CompletedTask;
        }
    };
});
builder.Services.AddScoped<IApplicationBuilder, ApplicationBuilder>(); // Adjust to your implementation
builder.Services.AddScoped<IUserRepository, UserRepository>();
builder.Services.AddScoped<ICurrentUserService, CurrentUserService>();
builder.Services.AddScoped<IUserIdAccessor, HttpContextUserIdAccessor>();
builder.Services.AddScoped<IDateTime, DateTimeService>();
builder.Services.AddScoped<IUnitOfWork, UnitOfWork>();
builder.Services.AddScoped<IPasswordHasher, PasswordHasher>();
builder.Services.AddScoped<IEmailService, EmailService>();
builder.Services.AddScoped<ITokenService, TokenService>();
builder.Services.AddScoped<IAuditLogService, AuditLogService>();

// Configure and register password complexity service
builder.Services.Configure<IdentityService.Domain.Services.PasswordComplexityOptions>(
    builder.Configuration.GetSection("PasswordComplexity"));
builder.Services.AddScoped<IPasswordComplexityService, PasswordComplexityService>();

// Configure and register session management service
builder.Services.Configure<IdentityService.Domain.Services.SessionManagementOptions>(
    builder.Configuration.GetSection("SessionManagement"));
builder.Services.AddScoped<ISessionManagementService, SessionManagementService>();

// Configure Authorization Policies
builder.Services.AddAuthorization(options =>
{
    // Default policy - requires authenticated user
    options.DefaultPolicy = new AuthorizationPolicyBuilder()
        .RequireAuthenticatedUser()
        .Build();

    // User management policies
    options.AddPolicy("Users.View", policy => policy.RequireClaim("permission", "Users.View"));
    options.AddPolicy("Users.Create", policy => policy.RequireClaim("permission", "Users.Create"));
    options.AddPolicy("Users.Update", policy => policy.RequireClaim("permission", "Users.Update"));
    options.AddPolicy("Users.Delete", policy => policy.RequireClaim("permission", "Users.Delete"));

    // Role management policies
    options.AddPolicy("Roles.View", policy => policy.RequireClaim("permission", "Roles.View"));
    options.AddPolicy("Roles.Create", policy => policy.RequireClaim("permission", "Roles.Create"));
    options.AddPolicy("Roles.Update", policy => policy.RequireClaim("permission", "Roles.Update"));
    options.AddPolicy("Roles.Delete", policy => policy.RequireClaim("permission", "Roles.Delete"));

    // Permission management policies
    options.AddPolicy("Permissions.View", policy => policy.RequireClaim("permission", "Permissions.View"));
    options.AddPolicy("Permissions.Create", policy => policy.RequireClaim("permission", "Permissions.Create"));
    options.AddPolicy("Permissions.Update", policy => policy.RequireClaim("permission", "Permissions.Update"));
    options.AddPolicy("Permissions.Delete", policy => policy.RequireClaim("permission", "Permissions.Delete"));
    options.AddPolicy("Permissions.Assign", policy => policy.RequireClaim("permission", "Permissions.Assign"));

    // Menu management policies
    options.AddPolicy("Menus.View", policy => policy.RequireClaim("permission", "Menus.View"));
    options.AddPolicy("Menus.Create", policy => policy.RequireClaim("permission", "Menus.Create"));
    options.AddPolicy("Menus.Update", policy => policy.RequireClaim("permission", "Menus.Update"));
    options.AddPolicy("Menus.Delete", policy => policy.RequireClaim("permission", "Menus.Delete"));

    // Audit log policies
    options.AddPolicy("AuditLogs.View", policy => policy.RequireClaim("permission", "AuditLogs.View"));

    // Subscription tier-based policies
    options.AddPolicy("Subscription.Basic", policy =>
        policy.RequireAuthenticatedUser().AddRequirements(new SubscriptionTierRequirement(SubscriptionTier.Basic)));
    options.AddPolicy("Subscription.Standard", policy =>
        policy.RequireAuthenticatedUser().AddRequirements(new SubscriptionTierRequirement(SubscriptionTier.Standard)));
    options.AddPolicy("Subscription.Premium", policy =>
        policy.RequireAuthenticatedUser().AddRequirements(new SubscriptionTierRequirement(SubscriptionTier.Premium)));
    options.AddPolicy("Subscription.Enterprise", policy =>
        policy.RequireAuthenticatedUser().AddRequirements(new SubscriptionTierRequirement(SubscriptionTier.Enterprise)));

    // Feature-based policies
    options.AddPolicy("Feature.MultipleUsers", policy =>
        policy.RequireAuthenticatedUser().AddRequirements(new SubscriptionFeatureRequirement("MultipleUsers")));
    options.AddPolicy("Feature.AdvancedReporting", policy =>
        policy.RequireAuthenticatedUser().AddRequirements(new SubscriptionFeatureRequirement("AdvancedReporting")));
    options.AddPolicy("Feature.BulkOperations", policy =>
        policy.RequireAuthenticatedUser().AddRequirements(new SubscriptionFeatureRequirement("BulkOperations")));
    options.AddPolicy("Feature.API", policy =>
        policy.RequireAuthenticatedUser().AddRequirements(new SubscriptionFeatureRequirement("API")));
});

// Register authorization handlers
builder.Services.AddScoped<IAuthorizationHandler, SubscriptionTierHandler>();
builder.Services.AddScoped<IAuthorizationHandler, SubscriptionFeatureHandler>();



// Configure Swagger
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo { Title = "Identity Service API", Version = "v1" });

    // Define the security scheme
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.Http,
        Scheme = "bearer",
        BearerFormat = "JWT"
    });

    // Make sure swagger UI requires a Bearer token specified
    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            new string[] {}
        }
    });

    // Add operation filter to add authorization header to all endpoints
    c.OperationFilter<SecurityRequirementsOperationFilter>();
});

// Add MediatR
builder.Services.AddMediatR(typeof(LoginCommand).Assembly);

// Add behaviors to MediatR pipeline
builder.Services.AddTransient(typeof(IPipelineBehavior<,>), typeof(UserIdPropagationBehavior<,>));
builder.Services.AddTransient(typeof(IPipelineBehavior<,>), typeof(ValidationBehavior<,>));

// Add FluentValidation
builder.Services.AddTransient<IValidator<LoginCommand>, IdentityService.Application.Validators.Auth.LoginCommandValidator>();
// Add other validators as needed

// Add AutoMapper
builder.Services.AddAutoMapper(typeof(Program).Assembly);

// Add controllers with validation
builder.Services.AddControllers(options => {
    options.Filters.Add<ValidationExceptionHandler>();
});

// Add MassTransit with RabbitMQ
builder.Services.AddMassTransitWithRabbitMq(builder.Configuration);

// Add service clients for direct API calls
builder.Services.AddTenantManagementServiceClient(builder.Configuration);
builder.Services.AddSubscriptionServiceClient(builder.Configuration);

// Add background services
builder.Services.AddHostedService<IdentityService.API.Services.SessionCleanupService>();

var app = builder.Build();
var option = new RewriteOptions();
option.AddRedirect("^$", "swagger");
app.UseRewriter(option);

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();

// Add global exception handling middleware
app.UseExceptionHandlingMiddleware();

app.UseAuthentication();
app.UseAuthorization();

// Add security stamp validation middleware after authentication and authorization
// This will invalidate tokens when security-critical changes are made (e.g., password change)
app.UseSecurityStampValidation();

// Add session validation middleware after authentication and authorization
app.UseSessionValidation();

// Add user ID propagation middleware after authentication and authorization
app.UseUserIdPropagation();

app.MapControllers();
app.UseSwagger();
app.UseSwaggerUI();

// Apply migrations
using (var scope = app.Services.CreateScope())
{
    var services = scope.ServiceProvider;
    try
    {
        var context = services.GetRequiredService<ApplicationDbContext>();
        context.Database.Migrate();
    }
    catch (Exception ex)
    {
        var logger = services.GetRequiredService<ILogger<Program>>();
        logger.LogError(ex, "An error occurred while migrating the database.");
    }
}

app.Run();
