using BranchManagementService.Application.DTOs;
using BranchManagementService.Application.Features.Branches.Commands;
using BranchManagementService.Application.Features.Branches.Queries;
using MediatR;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using VMS.Contracts.Common.Context;

namespace BranchManagementService.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [EnableCors("AllowAll")]
    public class BranchesController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly ILogger<BranchesController> _logger;
        private readonly IBranchContextService _branchContextService;

        public BranchesController(
            IMediator mediator,
            ILogger<BranchesController> logger,
            IBranchContextService branchContextService)
        {
            _mediator = mediator;
            _logger = logger;
            _branchContextService = branchContextService;
        }

        [HttpGet]
        public async Task<ActionResult<List<BranchDto>>> GetBranchesByVendorId([FromQuery] Guid? vendorId = null)
        {
            try
            {
                // Use vendor ID from context if not provided
                var contextVendorId = _branchContextService.GetVendorId();
                var effectiveVendorId = vendorId ?? contextVendorId;

                // Validate vendor access
                if (effectiveVendorId != contextVendorId && !_branchContextService.HasVendorAccess())
                {
                    _logger.LogWarning("Access denied to vendor {VendorId}", effectiveVendorId);
                    return StatusCode(StatusCodes.Status403Forbidden, new { message = "You do not have access to this vendor" });
                }

                var query = new GetBranchesByVendorIdQuery { VendorId = effectiveVendorId };
                var branches = await _mediator.Send(query);
                return Ok(branches);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting branches for vendor");
                return StatusCode(500, new { message = "An error occurred while retrieving branches" });
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<BranchDto>> GetBranchById(Guid id)
        {
            try
            {
                // Validate branch access
                if (!_branchContextService.HasAccessToBranch(id))
                {
                    _logger.LogWarning("Access denied to branch {BranchId}", id);
                    return StatusCode(StatusCodes.Status403Forbidden, new { message = "You do not have access to this branch" });
                }

                var query = new GetBranchByIdQuery { Id = id };
                var branch = await _mediator.Send(query);

                if (branch == null)
                {
                    return NotFound(new { message = $"Branch with ID {id} not found" });
                }

                return Ok(branch);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex, "Branch with ID {BranchId} not found", id);
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting branch with ID {BranchId}", id);
                return StatusCode(500, new { message = "An error occurred while retrieving the branch" });
            }
        }

        [HttpPost]
        public async Task<ActionResult<BranchDto>> CreateBranch([FromBody] CreateBranchCommand command)
        {
            try
            {
                // Validate vendor access
                var contextVendorId = _branchContextService.GetVendorId();

                // If vendor ID is provided, validate access
                if (command.VendorId != Guid.Empty && command.VendorId != contextVendorId && !_branchContextService.HasVendorAccess())
                {
                    _logger.LogWarning("Access denied to vendor {VendorId}", command.VendorId);
                    return StatusCode(StatusCodes.Status403Forbidden, new { message = "You do not have access to this vendor" });
                }

                // Set vendor ID from context if not provided
                if (command.VendorId == Guid.Empty)
                {
                    command.VendorId = contextVendorId;
                }

                // Only vendor admins can create branches
                if (!_branchContextService.IsVendorAdmin())
                {
                    _logger.LogWarning("User is not a vendor admin");
                    return StatusCode(StatusCodes.Status403Forbidden, new { message = "Only vendor administrators can create branches" });
                }

                var branch = await _mediator.Send(command);
                return CreatedAtAction(nameof(GetBranchById), new { id = branch.Id }, branch);
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Invalid operation during branch creation");
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating branch for vendor {VendorId}", command.VendorId);
                return StatusCode(500, new { message = "An error occurred while creating the branch" });
            }
        }

        [HttpPut("{id}")]
        public async Task<ActionResult<BranchDto>> UpdateBranch(Guid id, [FromBody] UpdateBranchCommand command)
        {
            try
            {
                if (id != command.Id)
                {
                    return BadRequest(new { message = "ID in URL does not match ID in request body" });
                }

                // Validate branch access
                if (!_branchContextService.HasAccessToBranch(id))
                {
                    _logger.LogWarning("Access denied to branch {BranchId}", id);
                    return StatusCode(StatusCodes.Status403Forbidden, new { message = "You do not have access to this branch" });
                }

                // Only vendor admins or branch admins can update branches
                if (!_branchContextService.IsVendorAdmin() && !_branchContextService.IsBranchAdmin())
                {
                    _logger.LogWarning("User is not a vendor admin or branch admin");
                    return StatusCode(StatusCodes.Status403Forbidden, new { message = "Only vendor or branch administrators can update branches" });
                }

                var branch = await _mediator.Send(command);
                return Ok(branch);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex, "Branch with ID {BranchId} not found", id);
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating branch with ID {BranchId}", id);
                return StatusCode(500, new { message = "An error occurred while updating the branch" });
            }
        }
    }
}
