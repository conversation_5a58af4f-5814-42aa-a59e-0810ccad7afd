using System.ComponentModel.DataAnnotations;

namespace IdentityService.Application.DTOs.Auth;

public class LoginRequest
{
    [EmailAddress]
    public string Email { get; set; }

    public string Password { get; set; }

    [Phone]
    public string? PhoneNumber { get; set; }

    public string? Otp { get; set; }

    public bool RememberMe { get; set; }

    public bool IsEmailLogin => !string.IsNullOrEmpty(Email) && !string.IsNullOrEmpty(Password);
    public bool IsPhoneLogin => !string.IsNullOrEmpty(PhoneNumber) && !string.IsNullOrEmpty(Otp);
}