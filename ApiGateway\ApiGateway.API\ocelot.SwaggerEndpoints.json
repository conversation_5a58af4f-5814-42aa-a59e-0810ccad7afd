{"SwaggerEndPoints": [{"Key": "branch", "Config": [{"Name": "Branch Management API", "Version": "v1", "Url": "http://localhost:5000/swagger/branch/swagger.json"}]}, {"Key": "identity", "Config": [{"Name": "Identity API", "Version": "v1", "Url": "http://localhost:5000/swagger/identity/swagger.json"}]}, {"Key": "subscription", "Config": [{"Name": "Subscription API", "Version": "v1", "Url": "http://localhost:5000/swagger/subscription/swagger.json"}]}, {"Key": "tenant", "Config": [{"Name": "Tenant Management API", "Version": "v1", "Url": "http://localhost:5000/swagger/tenant/swagger.json"}]}, {"Key": "vehicle", "Config": [{"Name": "Vehicle Management API", "Version": "v1", "Url": "http://localhost:5000/swagger/vehicle/swagger.json"}]}]}