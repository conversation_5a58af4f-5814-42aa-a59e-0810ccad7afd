using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using IdentityService.Application.DTOs.Menus;
using IdentityService.Application.Interfaces;
using IdentityService.Domain.Interfaces;
using MediatR;

namespace IdentityService.Application.Features.Menus.Commands;

public class AssignMenuPermissionCommand : IRequest<MenuResponse>
{
    public Guid MenuId { get; set; }
    public AssignMenuPermissionRequest Request { get; set; }
}

public class AssignMenuPermissionCommandHandler : IRequestHandler<AssignMenuPermissionCommand, MenuResponse>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICurrentUserService _currentUserService;
    private readonly IAuditLogService _auditLogService;

    public AssignMenuPermissionCommandHandler(
        IUnitOfWork unitOfWork,
        ICurrentUserService currentUserService,
        IAuditLogService auditLogService)
    {
        _unitOfWork = unitOfWork;
        _currentUserService = currentUserService;
        _auditLogService = auditLogService;
    }

    public async Task<MenuResponse> Handle(AssignMenuPermissionCommand command, CancellationToken cancellationToken)
    {
        var menu = await _unitOfWork.MenuRepository.GetByIdAsync(command.MenuId);
        if (menu == null)
            throw new InvalidOperationException($"Menu with ID {command.MenuId} not found");
            
        var request = command.Request;
        var updatedBy = _currentUserService.UserId.ToString() ?? "System";
        
        // Track current permissions for audit log
        var currentPermissions = new List<object>();
        foreach (var mp in menu.MenuPermissions)
        {
            if (mp.PermissionId.HasValue)
            {
                var permission = await _unitOfWork.PermissionRepository.GetByIdAsync(mp.PermissionId.Value);
                if (permission != null)
                {
                    currentPermissions.Add(new { permission.Id, permission.Name });
                }
            }
            else
            {
                currentPermissions.Add(new { Id = Guid.Empty, Name = "Public Access" });
            }
        }
        var oldValues = System.Text.Json.JsonSerializer.Serialize(currentPermissions);
        
        // Remove existing permissions
        menu.ClearPermissions();
        
        // Add new permissions
        foreach (var permissionId in request.PermissionIds)
        {
            var permission = await _unitOfWork.PermissionRepository.GetByIdAsync(permissionId);
            if (permission != null)
            {
                menu.AddPermission(permission, updatedBy);
            }
        }
        
        // Save changes
        await _unitOfWork.MenuRepository.UpdateAsync(menu);
        await _unitOfWork.SaveChangesAsync(cancellationToken);
        
        // Get updated permissions for audit log and response
        var updatedPermissions = new List<object>();
        var responsePermissions = new List<MenuPermissionResponse>();
        foreach (var mp in menu.MenuPermissions)
        {
            if (mp.PermissionId.HasValue)
            {
                var permission = await _unitOfWork.PermissionRepository.GetByIdAsync(mp.PermissionId.Value);
                if (permission != null)
                {
                    updatedPermissions.Add(new { permission.Id, permission.Name });
                    responsePermissions.Add(new MenuPermissionResponse
                    {
                        Id = permission.Id,
                        Name = permission.Name,
                        Description = permission.Description,
                        Resource = permission.Resource,
                        Action = permission.Action
                    });
                }
            }
            else
            {
                updatedPermissions.Add(new { Id = Guid.Empty, Name = "Public Access" });
                responsePermissions.Add(new MenuPermissionResponse
                {
                    Id = Guid.Empty,
                    Name = "Public Access",
                    Description = "Accessible to all authenticated users",
                    Resource = "Menu",
                    Action = "View"
                });
            }
        }
        var newValues = System.Text.Json.JsonSerializer.Serialize(updatedPermissions);
        
        // Create audit log
        await _auditLogService.CreateAuditLogAsync(
            "AssignPermissions",
            "Menu",
            menu.Id.ToString(),
            oldValues,
            newValues,
            "MenuPermissions",
            _currentUserService.UserId ?? Guid.Empty);
        
        // Create response
        var response = new MenuResponse
        {
            Id = menu.Id,
            Name = menu.Name,
            DisplayName = menu.DisplayName,
            Path = menu.Path,
            Icon = menu.Icon,
            Order = menu.Order,
            ParentId = menu.ParentId,
            CreatedAt = menu.CreatedAt,
            CreatedBy = menu.CreatedBy,
            UpdatedAt = menu.UpdatedAt,
            UpdatedBy = menu.UpdatedBy,
            Permissions = responsePermissions
        };
        
        return response;
    }
}
