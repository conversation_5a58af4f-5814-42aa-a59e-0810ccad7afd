{"version": "0.2.0", "configurations": [{"name": "BranchManagementService", "type": "coreclr", "request": "launch", "preLaunchTask": "build-branch", "program": "${workspaceFolder:BranchManagementService}/src/BranchManagementService.API/bin/Debug/net8.0/BranchManagementService.API.dll", "args": ["--urls", "http://localhost:5004"], "cwd": "${workspaceFolder:BranchManagementService}/src/BranchManagementService.API", "stopAtEntry": false, "serverReadyAction": {"action": "openExternally", "pattern": "\\bNow listening on:\\s+(https?://\\S+)", "uriFormat": "%s/swagger"}, "env": {"ASPNETCORE_ENVIRONMENT": "Development"}, "sourceFileMap": {"/Views": "${workspaceFolder:BranchManagementService}/Views"}}, {"name": ".NET Core Attach", "type": "coreclr", "request": "attach"}]}