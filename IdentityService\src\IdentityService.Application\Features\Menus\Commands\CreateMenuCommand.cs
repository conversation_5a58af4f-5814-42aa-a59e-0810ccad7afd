using System;
using System.Threading;
using System.Threading.Tasks;
using IdentityService.Application.DTOs.Menus;
using IdentityService.Application.Interfaces;
using IdentityService.Domain.Entities;
using IdentityService.Domain.Interfaces;
using MediatR;

namespace IdentityService.Application.Features.Menus.Commands;

public class CreateMenuCommand : IRequest<MenuResponse>
{
    public CreateMenuRequest Request { get; set; }
}

public class CreateMenuCommandHandler : IRequestHandler<CreateMenuCommand, MenuResponse>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICurrentUserService _currentUserService;
    private readonly IAuditLogService _auditLogService;

    public CreateMenuCommandHandler(
        IUnitOfWork unitOfWork,
        ICurrentUserService currentUserService,
        IAuditLogService auditLogService)
    {
        _unitOfWork = unitOfWork;
        _currentUserService = currentUserService;
        _auditLogService = auditLogService;
    }

    public async Task<MenuResponse> Handle(CreateMenuCommand command, CancellationToken cancellationToken)
    {
        var request = command.Request;
        var createdBy = _currentUserService.UserId.ToString() ?? "System";
        
        // Validate parent menu if provided
        if (request.ParentId.HasValue)
        {
            var parentMenu = await _unitOfWork.MenuRepository.GetByIdAsync(request.ParentId.Value);
            if (parentMenu == null)
                throw new InvalidOperationException($"Parent menu with ID {request.ParentId.Value} not found");
        }
        
        // Create menu
        var menu = Menu.Create(
            request.Name,
            request.DisplayName,
            request.Path,
            request.Icon,
            request.Order,
            request.ParentId,
            createdBy);
        
        // Save menu
        await _unitOfWork.MenuRepository.AddAsync(menu);
        await _unitOfWork.SaveChangesAsync(cancellationToken);
        
        // Create audit log
        await _auditLogService.CreateAuditLogAsync(
            "Create",
            "Menu",
            menu.Id.ToString(),
            string.Empty,
            System.Text.Json.JsonSerializer.Serialize(new { 
                menu.Id, 
                menu.Name, 
                menu.DisplayName, 
                menu.Path, 
                menu.Icon, 
                menu.Order, 
                menu.ParentId 
            }),
            "Id,Name,DisplayName,Path,Icon,Order,ParentId",
            _currentUserService.UserId ?? Guid.Empty);
        
        // Create response
        var response = new MenuResponse
        {
            Id = menu.Id,
            Name = menu.Name,
            DisplayName = menu.DisplayName,
            Path = menu.Path,
            Icon = menu.Icon,
            Order = menu.Order,
            ParentId = menu.ParentId,
            CreatedAt = menu.CreatedAt,
            CreatedBy = menu.CreatedBy,
            UpdatedAt = menu.UpdatedAt,
            UpdatedBy = menu.UpdatedBy
        };
        
        return response;
    }
}
