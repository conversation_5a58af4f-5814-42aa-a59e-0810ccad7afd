using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using IdentityService.Application.Common;
using IdentityService.Application.DTOs.AuditLogs;
using IdentityService.Application.Interfaces;
using MediatR;

namespace IdentityService.Application.Features.AuditLogs.Queries;

public class ExportAuditLogsQuery : BaseRequest<byte[]>
{
    public AuditLogFilterRequest Filter { get; set; }
    public string ExportFormat { get; set; } = "csv"; // csv or pdf
}

public class ExportAuditLogsQueryHandler : IRequestHandler<ExportAuditLogsQuery, byte[]>
{
    private readonly IAuditLogService _auditLogService;

    public ExportAuditLogsQueryHandler(IAuditLogService auditLogService)
    {
        _auditLogService = auditLogService;
    }

    public async Task<byte[]> Handle(ExportAuditLogsQuery query, CancellationToken cancellationToken)
    {
        var filter = query.Filter;

        // Get all audit logs matching the filter (without pagination)
        var (auditLogs, _) = await _auditLogService.GetFilteredAuditLogsAsync(
            filter.Action,
            filter.EntityName,
            filter.EntityId,
            filter.UserId,
            filter.FromDate,
            filter.ToDate,
            1, // Page number
            int.MaxValue); // Get all records

        // Export based on format
        if (query.ExportFormat.ToLower() == "pdf")
        {
            return await _auditLogService.ExportToPdfAsync(auditLogs);
        }
        else
        {
            return await _auditLogService.ExportToCsvAsync(auditLogs);
        }
    }
}
