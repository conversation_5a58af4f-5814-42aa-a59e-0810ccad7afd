using System;
using System.Collections.Generic;

namespace IdentityService.Application.DTOs.Auth;

public class LoginResponse
{
    public string AccessToken { get; set; }
    public string RefreshToken { get; set; }
    public DateTime AccessTokenExpiryTime { get; set; }
    public DateTime RefreshTokenExpiryTime { get; set; }
    public UserDto User { get; set; }
    public IList<string> Roles { get; set; }
    public IList<string> Permissions { get; set; }
}

public class UserDto
{
    public Guid Id { get; set; }
    public string Email { get; set; }
    public string PhoneNumber { get; set; }
    public bool EmailVerified { get; set; }
    public bool PhoneNumberVerified { get; set; }
    public DateTime? LastLoginAt { get; set; }
} 