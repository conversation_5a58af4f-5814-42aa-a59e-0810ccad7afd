2025-06-06 10:09:42.908 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'AuditLog'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-06 10:09:43.059 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'PasswordResetToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-06 10:09:43.107 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserBranch'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-06 10:09:43.135 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserRole'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-06 10:09:43.154 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserSession'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-06 10:09:43.162 +05:30 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserSubscription'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-06 10:09:43.907 +05:30 [INF] Executed DbCommand (61ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-06-06 10:09:43.940 +05:30 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-06-06 10:09:44.024 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-06-06 10:09:44.092 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-06-06 10:09:44.096 +05:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-06-06 10:09:44.116 +05:30 [INF] No migrations were applied. The database is already up to date.
2025-06-06 10:09:44.145 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-06 10:09:44.346 +05:30 [INF] Configured endpoint SubscriptionCreated, Consumer: IdentityService.Infrastructure.Messaging.Consumers.SubscriptionCreatedConsumer
2025-06-06 10:09:44.354 +05:30 [INF] Configured endpoint SubscriptionStatusChanged, Consumer: IdentityService.Infrastructure.Messaging.Consumers.SubscriptionStatusChangedConsumer
2025-06-06 10:09:44.364 +05:30 [INF] Configured endpoint VendorRegistered, Consumer: IdentityService.Infrastructure.Messaging.Consumers.VendorRegisteredConsumer
2025-06-06 10:09:44.595 +05:30 [DBG] Starting bus instances: IBus
2025-06-06 10:09:44.600 +05:30 [DBG] Starting bus: "rabbitmq://localhost/"
2025-06-06 10:09:44.636 +05:30 [INF] Session cleanup service is starting
2025-06-06 10:09:44.641 +05:30 [DBG] Starting session cleanup
2025-06-06 10:09:44.678 +05:30 [DBG] Connect: guest@localhost:5672/
2025-06-06 10:09:44.760 +05:30 [DBG] Connected: guest@localhost:5672/ (address: amqp://localhost:5672, local: 54727)
2025-06-06 10:09:44.800 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/DESKTOPI7D4M6U_IdentityServiceAPI_bus_kyooyyfp37kqnt5dbdq4jpbego?temporary=true"
2025-06-06 10:09:44.831 +05:30 [DBG] Declare queue: name: SubscriptionStatusChanged, durable, consumer-count: 0 message-count: 0
2025-06-06 10:09:44.831 +05:30 [DBG] Declare queue: name: SubscriptionCreated, durable, consumer-count: 1 message-count: 0
2025-06-06 10:09:44.831 +05:30 [DBG] Declare queue: name: VendorRegistered, durable, consumer-count: 0 message-count: 0
2025-06-06 10:09:44.849 +05:30 [DBG] Declare exchange: name: SubscriptionCreated, type: fanout, durable
2025-06-06 10:09:44.849 +05:30 [DBG] Declare exchange: name: SubscriptionStatusChanged, type: fanout, durable
2025-06-06 10:09:44.856 +05:30 [DBG] Declare exchange: name: VMS.Contracts.Subscription.Events:SubscriptionStatusChanged, type: fanout, durable
2025-06-06 10:09:44.856 +05:30 [DBG] Declare exchange: name: VMS.Contracts.Subscription.Events:SubscriptionCreated, type: fanout, durable
2025-06-06 10:09:44.857 +05:30 [DBG] Declare exchange: name: VendorRegistered, type: fanout, durable
2025-06-06 10:09:44.861 +05:30 [DBG] Declare exchange: name: VMS.Contracts.Tenant.Events:VendorRegistered, type: fanout, durable
2025-06-06 10:09:44.869 +05:30 [DBG] Bind queue: source: SubscriptionStatusChanged, destination: SubscriptionStatusChanged
2025-06-06 10:09:44.869 +05:30 [DBG] Bind queue: source: SubscriptionCreated, destination: SubscriptionCreated
2025-06-06 10:09:44.869 +05:30 [DBG] Bind queue: source: VendorRegistered, destination: VendorRegistered
2025-06-06 10:09:44.912 +05:30 [DBG] Bind exchange: source: VMS.Contracts.Subscription.Events:SubscriptionCreated, destination: SubscriptionCreated
2025-06-06 10:09:44.912 +05:30 [DBG] Bind exchange: source: VMS.Contracts.Tenant.Events:VendorRegistered, destination: VendorRegistered
2025-06-06 10:09:44.912 +05:30 [DBG] Bind exchange: source: VMS.Contracts.Subscription.Events:SubscriptionStatusChanged, destination: SubscriptionStatusChanged
2025-06-06 10:09:44.967 +05:30 [DBG] Consumer Ok: "rabbitmq://localhost/VendorRegistered" - amq.ctag-sXTAs43jj097Kp3Yr-l6cA
2025-06-06 10:09:44.967 +05:30 [DBG] Consumer Ok: "rabbitmq://localhost/SubscriptionCreated" - amq.ctag-VTFC6ZRmDk6D06sYM2hJFQ
2025-06-06 10:09:44.967 +05:30 [DBG] Consumer Ok: "rabbitmq://localhost/SubscriptionStatusChanged" - amq.ctag-oxkTt8Y4Os8BmxnM2ls4XQ
2025-06-06 10:09:44.970 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/SubscriptionCreated"
2025-06-06 10:09:44.970 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/VendorRegistered"
2025-06-06 10:09:44.972 +05:30 [DBG] Endpoint Ready: "rabbitmq://localhost/SubscriptionStatusChanged"
2025-06-06 10:09:44.980 +05:30 [INF] Bus started: "rabbitmq://localhost/"
2025-06-06 10:09:45.440 +05:30 [ERR] Failed executing DbCommand (35ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-06-06 10:09:45.491 +05:30 [ERR] An exception occurred while iterating over the results of a query for context type 'IdentityService.Infrastructure.Persistence.ApplicationDbContext'.
Npgsql.PostgresException (0x80004005): 42703: column u0.PrimaryBranchId does not exist

POSITION: 806
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column u0.PrimaryBranchId does not exist
    Position: 806
    File: parse_relation.c
    Line: 3718
    Routine: errorMissingColumn
Npgsql.PostgresException (0x80004005): 42703: column u0.PrimaryBranchId does not exist

POSITION: 806
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column u0.PrimaryBranchId does not exist
    Position: 806
    File: parse_relation.c
    Line: 3718
    Routine: errorMissingColumn
2025-06-06 10:09:45.526 +05:30 [ERR] Error occurred while cleaning up sessions
Npgsql.PostgresException (0x80004005): 42703: column u0.PrimaryBranchId does not exist

POSITION: 806
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at IdentityService.Infrastructure.Persistence.Repositories.UserSessionRepository.GetIdleSessionsAsync(Int32 idleTimeoutMinutes) in D:\Projects\VehicleManagementSystem\VMS-API\IdentityService\src\IdentityService.Infrastructure\Persistence\Repositories\UserSessionRepository.cs:line 95
   at IdentityService.Infrastructure.Services.SessionManagementService.ExpireIdleSessionsAsync() in D:\Projects\VehicleManagementSystem\VMS-API\IdentityService\src\IdentityService.Infrastructure\Services\SessionManagementService.cs:line 234
   at IdentityService.API.Services.SessionCleanupService.CleanupSessionsAsync() in D:\Projects\VehicleManagementSystem\VMS-API\IdentityService\src\IdentityService.API\Services\SessionCleanupService.cs:line 56
   at IdentityService.API.Services.SessionCleanupService.ExecuteAsync(CancellationToken stoppingToken) in D:\Projects\VehicleManagementSystem\VMS-API\IdentityService\src\IdentityService.API\Services\SessionCleanupService.cs:line 36
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column u0.PrimaryBranchId does not exist
    Position: 806
    File: parse_relation.c
    Line: 3718
    Routine: errorMissingColumn
2025-06-06 10:09:45.542 +05:30 [INF] Now listening on: http://localhost:5263
2025-06-06 10:09:45.597 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-06-06 10:09:45.599 +05:30 [INF] Hosting environment: Development
2025-06-06 10:09:45.601 +05:30 [INF] Content root path: D:\Projects\VehicleManagementSystem\VMS-API\IdentityService\src\IdentityService.API
2025-06-06 10:09:47.435 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/ - null null
2025-06-06 10:09:47.560 +05:30 [INF] Request was redirected to /swagger
2025-06-06 10:09:47.568 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/ - 302 0 null 136.3483ms
2025-06-06 10:09:47.637 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/swagger - null null
2025-06-06 10:09:47.694 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/swagger - 301 0 null 56.6358ms
2025-06-06 10:09:47.700 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/swagger/index.html - null null
2025-06-06 10:09:47.844 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/swagger/index.html - 200 null text/html;charset=utf-8 143.6443ms
2025-06-06 10:09:47.864 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/swagger/swagger-ui.css - null null
2025-06-06 10:09:47.866 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/swagger/swagger-ui-bundle.js - null null
2025-06-06 10:09:47.867 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/swagger/swagger-ui-standalone-preset.js - null null
2025-06-06 10:09:47.920 +05:30 [INF] Sending file. Request path: '/swagger-ui-standalone-preset.js'. Physical path: 'N/A'
2025-06-06 10:09:47.920 +05:30 [INF] Sending file. Request path: '/swagger-ui.css'. Physical path: 'N/A'
2025-06-06 10:09:47.924 +05:30 [INF] Sending file. Request path: '/swagger-ui-bundle.js'. Physical path: 'N/A'
2025-06-06 10:09:47.989 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/swagger/swagger-ui-standalone-preset.js - 200 339486 text/javascript 122.1356ms
2025-06-06 10:09:47.992 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/swagger/swagger-ui.css - 200 143943 text/css 128.1015ms
2025-06-06 10:09:47.999 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/swagger/swagger-ui-bundle.js - 200 1096145 text/javascript 132.8948ms
2025-06-06 10:09:48.393 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/swagger/v1/swagger.json - null null
2025-06-06 10:09:48.443 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5263/swagger/favicon-32x32.png - null null
2025-06-06 10:09:48.450 +05:30 [INF] Sending file. Request path: '/favicon-32x32.png'. Physical path: 'N/A'
2025-06-06 10:09:48.465 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/swagger/favicon-32x32.png - 200 628 image/png 22.2465ms
2025-06-06 10:09:49.298 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5263/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 904.6027ms
2025-06-06 10:14:45.608 +05:30 [DBG] Starting session cleanup
2025-06-06 10:14:45.987 +05:30 [ERR] Failed executing DbCommand (4ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-06-06 10:14:46.017 +05:30 [ERR] An exception occurred while iterating over the results of a query for context type 'IdentityService.Infrastructure.Persistence.ApplicationDbContext'.
Npgsql.PostgresException (0x80004005): 42703: column u0.PrimaryBranchId does not exist

POSITION: 806
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column u0.PrimaryBranchId does not exist
    Position: 806
    File: parse_relation.c
    Line: 3718
    Routine: errorMissingColumn
Npgsql.PostgresException (0x80004005): 42703: column u0.PrimaryBranchId does not exist

POSITION: 806
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column u0.PrimaryBranchId does not exist
    Position: 806
    File: parse_relation.c
    Line: 3718
    Routine: errorMissingColumn
2025-06-06 10:14:46.072 +05:30 [ERR] Error occurred while cleaning up sessions
Npgsql.PostgresException (0x80004005): 42703: column u0.PrimaryBranchId does not exist

POSITION: 806
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at IdentityService.Infrastructure.Persistence.Repositories.UserSessionRepository.GetIdleSessionsAsync(Int32 idleTimeoutMinutes) in D:\Projects\VehicleManagementSystem\VMS-API\IdentityService\src\IdentityService.Infrastructure\Persistence\Repositories\UserSessionRepository.cs:line 95
   at IdentityService.Infrastructure.Services.SessionManagementService.ExpireIdleSessionsAsync() in D:\Projects\VehicleManagementSystem\VMS-API\IdentityService\src\IdentityService.Infrastructure\Services\SessionManagementService.cs:line 234
   at IdentityService.API.Services.SessionCleanupService.CleanupSessionsAsync() in D:\Projects\VehicleManagementSystem\VMS-API\IdentityService\src\IdentityService.API\Services\SessionCleanupService.cs:line 56
   at IdentityService.API.Services.SessionCleanupService.ExecuteAsync(CancellationToken stoppingToken) in D:\Projects\VehicleManagementSystem\VMS-API\IdentityService\src\IdentityService.API\Services\SessionCleanupService.cs:line 36
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column u0.PrimaryBranchId does not exist
    Position: 806
    File: parse_relation.c
    Line: 3718
    Routine: errorMissingColumn
2025-06-06 10:19:46.114 +05:30 [DBG] Starting session cleanup
2025-06-06 10:19:46.155 +05:30 [ERR] Failed executing DbCommand (6ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-06-06 10:19:46.181 +05:30 [ERR] An exception occurred while iterating over the results of a query for context type 'IdentityService.Infrastructure.Persistence.ApplicationDbContext'.
Npgsql.PostgresException (0x80004005): 42703: column u0.PrimaryBranchId does not exist

POSITION: 806
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column u0.PrimaryBranchId does not exist
    Position: 806
    File: parse_relation.c
    Line: 3718
    Routine: errorMissingColumn
Npgsql.PostgresException (0x80004005): 42703: column u0.PrimaryBranchId does not exist

POSITION: 806
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column u0.PrimaryBranchId does not exist
    Position: 806
    File: parse_relation.c
    Line: 3718
    Routine: errorMissingColumn
2025-06-06 10:19:46.278 +05:30 [ERR] Error occurred while cleaning up sessions
Npgsql.PostgresException (0x80004005): 42703: column u0.PrimaryBranchId does not exist

POSITION: 806
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at IdentityService.Infrastructure.Persistence.Repositories.UserSessionRepository.GetIdleSessionsAsync(Int32 idleTimeoutMinutes) in D:\Projects\VehicleManagementSystem\VMS-API\IdentityService\src\IdentityService.Infrastructure\Persistence\Repositories\UserSessionRepository.cs:line 95
   at IdentityService.Infrastructure.Services.SessionManagementService.ExpireIdleSessionsAsync() in D:\Projects\VehicleManagementSystem\VMS-API\IdentityService\src\IdentityService.Infrastructure\Services\SessionManagementService.cs:line 234
   at IdentityService.API.Services.SessionCleanupService.CleanupSessionsAsync() in D:\Projects\VehicleManagementSystem\VMS-API\IdentityService\src\IdentityService.API\Services\SessionCleanupService.cs:line 56
   at IdentityService.API.Services.SessionCleanupService.ExecuteAsync(CancellationToken stoppingToken) in D:\Projects\VehicleManagementSystem\VMS-API\IdentityService\src\IdentityService.API\Services\SessionCleanupService.cs:line 36
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column u0.PrimaryBranchId does not exist
    Position: 806
    File: parse_relation.c
    Line: 3718
    Routine: errorMissingColumn
2025-06-06 10:24:46.306 +05:30 [DBG] Starting session cleanup
2025-06-06 10:24:46.375 +05:30 [ERR] Failed executing DbCommand (2ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-06-06 10:24:46.380 +05:30 [ERR] An exception occurred while iterating over the results of a query for context type 'IdentityService.Infrastructure.Persistence.ApplicationDbContext'.
Npgsql.PostgresException (0x80004005): 42703: column u0.PrimaryBranchId does not exist

POSITION: 806
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column u0.PrimaryBranchId does not exist
    Position: 806
    File: parse_relation.c
    Line: 3718
    Routine: errorMissingColumn
Npgsql.PostgresException (0x80004005): 42703: column u0.PrimaryBranchId does not exist

POSITION: 806
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column u0.PrimaryBranchId does not exist
    Position: 806
    File: parse_relation.c
    Line: 3718
    Routine: errorMissingColumn
2025-06-06 10:24:46.393 +05:30 [ERR] Error occurred while cleaning up sessions
Npgsql.PostgresException (0x80004005): 42703: column u0.PrimaryBranchId does not exist

POSITION: 806
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at IdentityService.Infrastructure.Persistence.Repositories.UserSessionRepository.GetIdleSessionsAsync(Int32 idleTimeoutMinutes) in D:\Projects\VehicleManagementSystem\VMS-API\IdentityService\src\IdentityService.Infrastructure\Persistence\Repositories\UserSessionRepository.cs:line 95
   at IdentityService.Infrastructure.Services.SessionManagementService.ExpireIdleSessionsAsync() in D:\Projects\VehicleManagementSystem\VMS-API\IdentityService\src\IdentityService.Infrastructure\Services\SessionManagementService.cs:line 234
   at IdentityService.API.Services.SessionCleanupService.CleanupSessionsAsync() in D:\Projects\VehicleManagementSystem\VMS-API\IdentityService\src\IdentityService.API\Services\SessionCleanupService.cs:line 56
   at IdentityService.API.Services.SessionCleanupService.ExecuteAsync(CancellationToken stoppingToken) in D:\Projects\VehicleManagementSystem\VMS-API\IdentityService\src\IdentityService.API\Services\SessionCleanupService.cs:line 36
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column u0.PrimaryBranchId does not exist
    Position: 806
    File: parse_relation.c
    Line: 3718
    Routine: errorMissingColumn
2025-06-06 10:29:46.427 +05:30 [DBG] Starting session cleanup
2025-06-06 10:29:46.442 +05:30 [ERR] Failed executing DbCommand (2ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-06-06 10:29:46.458 +05:30 [ERR] An exception occurred while iterating over the results of a query for context type 'IdentityService.Infrastructure.Persistence.ApplicationDbContext'.
Npgsql.PostgresException (0x80004005): 42703: column u0.PrimaryBranchId does not exist

POSITION: 806
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column u0.PrimaryBranchId does not exist
    Position: 806
    File: parse_relation.c
    Line: 3718
    Routine: errorMissingColumn
Npgsql.PostgresException (0x80004005): 42703: column u0.PrimaryBranchId does not exist

POSITION: 806
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column u0.PrimaryBranchId does not exist
    Position: 806
    File: parse_relation.c
    Line: 3718
    Routine: errorMissingColumn
2025-06-06 10:29:46.496 +05:30 [ERR] Error occurred while cleaning up sessions
Npgsql.PostgresException (0x80004005): 42703: column u0.PrimaryBranchId does not exist

POSITION: 806
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at IdentityService.Infrastructure.Persistence.Repositories.UserSessionRepository.GetIdleSessionsAsync(Int32 idleTimeoutMinutes) in D:\Projects\VehicleManagementSystem\VMS-API\IdentityService\src\IdentityService.Infrastructure\Persistence\Repositories\UserSessionRepository.cs:line 95
   at IdentityService.Infrastructure.Services.SessionManagementService.ExpireIdleSessionsAsync() in D:\Projects\VehicleManagementSystem\VMS-API\IdentityService\src\IdentityService.Infrastructure\Services\SessionManagementService.cs:line 234
   at IdentityService.API.Services.SessionCleanupService.CleanupSessionsAsync() in D:\Projects\VehicleManagementSystem\VMS-API\IdentityService\src\IdentityService.API\Services\SessionCleanupService.cs:line 56
   at IdentityService.API.Services.SessionCleanupService.ExecuteAsync(CancellationToken stoppingToken) in D:\Projects\VehicleManagementSystem\VMS-API\IdentityService\src\IdentityService.API\Services\SessionCleanupService.cs:line 36
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column u0.PrimaryBranchId does not exist
    Position: 806
    File: parse_relation.c
    Line: 3718
    Routine: errorMissingColumn
2025-06-06 10:34:46.529 +05:30 [DBG] Starting session cleanup
2025-06-06 10:34:46.594 +05:30 [ERR] Failed executing DbCommand (2ms) [Parameters=[@__idleThreshold_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."CreatedAt", u."CreatedBy", u."DeviceInfo", u."EndReason", u."EndedAt", u."ExpiresAt", u."IpAddress", u."IsDeleted", u."LastActiveAt", u."RefreshToken", u."RowVersion", u."StartedAt", u."Status", u."Token", u."UpdatedAt", u."UpdatedBy", u."UserAgent", u."UserId", t."Id", t."CreatedAt", t."CreatedBy", t."Email", t."EmailVerified", t."IsDeleted", t."LastLoginAt", t."PhoneNumber", t."PhoneNumberVerified", t."PrimaryBranchId", t."RefreshToken", t."RefreshTokenExpiryTime", t."RememberMe", t."RowVersion", t."SecurityStamp", t."UpdatedAt", t."UpdatedBy", t."VendorId", t."PasswordHash"
FROM "UserSessions" AS u
INNER JOIN (
    SELECT u0."Id", u0."CreatedAt", u0."CreatedBy", u0."Email", u0."EmailVerified", u0."IsDeleted", u0."LastLoginAt", u0."PhoneNumber", u0."PhoneNumberVerified", u0."PrimaryBranchId", u0."RefreshToken", u0."RefreshTokenExpiryTime", u0."RememberMe", u0."RowVersion", u0."SecurityStamp", u0."UpdatedAt", u0."UpdatedBy", u0."VendorId", u0."PasswordHash"
    FROM "Users" AS u0
    WHERE NOT (u0."IsDeleted")
) AS t ON u."UserId" = t."Id"
WHERE u."Status" = 0 AND u."LastActiveAt" < @__idleThreshold_0 AND u."ExpiresAt" > now()
2025-06-06 10:34:46.600 +05:30 [ERR] An exception occurred while iterating over the results of a query for context type 'IdentityService.Infrastructure.Persistence.ApplicationDbContext'.
Npgsql.PostgresException (0x80004005): 42703: column u0.PrimaryBranchId does not exist

POSITION: 806
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column u0.PrimaryBranchId does not exist
    Position: 806
    File: parse_relation.c
    Line: 3718
    Routine: errorMissingColumn
Npgsql.PostgresException (0x80004005): 42703: column u0.PrimaryBranchId does not exist

POSITION: 806
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column u0.PrimaryBranchId does not exist
    Position: 806
    File: parse_relation.c
    Line: 3718
    Routine: errorMissingColumn
2025-06-06 10:34:46.615 +05:30 [ERR] Error occurred while cleaning up sessions
Npgsql.PostgresException (0x80004005): 42703: column u0.PrimaryBranchId does not exist

POSITION: 806
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at IdentityService.Infrastructure.Persistence.Repositories.UserSessionRepository.GetIdleSessionsAsync(Int32 idleTimeoutMinutes) in D:\Projects\VehicleManagementSystem\VMS-API\IdentityService\src\IdentityService.Infrastructure\Persistence\Repositories\UserSessionRepository.cs:line 95
   at IdentityService.Infrastructure.Services.SessionManagementService.ExpireIdleSessionsAsync() in D:\Projects\VehicleManagementSystem\VMS-API\IdentityService\src\IdentityService.Infrastructure\Services\SessionManagementService.cs:line 234
   at IdentityService.API.Services.SessionCleanupService.CleanupSessionsAsync() in D:\Projects\VehicleManagementSystem\VMS-API\IdentityService\src\IdentityService.API\Services\SessionCleanupService.cs:line 56
   at IdentityService.API.Services.SessionCleanupService.ExecuteAsync(CancellationToken stoppingToken) in D:\Projects\VehicleManagementSystem\VMS-API\IdentityService\src\IdentityService.API\Services\SessionCleanupService.cs:line 36
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column u0.PrimaryBranchId does not exist
    Position: 806
    File: parse_relation.c
    Line: 3718
    Routine: errorMissingColumn
