using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using IdentityService.Application.DTOs.Users;
using IdentityService.Application.Features.Users.Commands;
using IdentityService.Application.Features.Users.Queries;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace IdentityService.API.Controllers;

[ApiController]
[Route("api/[controller]")]
//[Authorize]
public class UsersController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<UsersController> _logger;

    public UsersController(IMediator mediator, ILogger<UsersController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    [HttpGet]
    [Authorize(Policy = "Users.View")]
    public async Task<ActionResult<List<UserResponse>>> GetUsers([FromQuery] bool includeInactive = false)
    {
        try
        {
            var query = new GetUsersQuery { IncludeInactive = includeInactive };
            var users = await _mediator.Send(query);
            return Ok(users);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving users");
            return StatusCode(500, new { message = "An error occurred while retrieving users" });
        }
    }

    [HttpGet("{id}")]
    //[Authorize(Policy = "Users.View")]
    public async Task<ActionResult<UserResponse>> GetUserById(Guid id)
    {
        try
        {
            var query = new GetUserByIdQuery { UserId = id };
            var user = await _mediator.Send(query);
            return Ok(user);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "User not found");
            return NotFound(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving user");
            return StatusCode(500, new { message = "An error occurred while retrieving the user" });
        }
    }

    [HttpPost]
    //[Authorize(Policy = "Users.Create")]
    public async Task<ActionResult<UserResponse>> CreateUser([FromBody] CreateUserRequest request)
    {
        try
        {
            var command = new CreateUserCommand { Request = request };
            var user = await _mediator.Send(command);
            return CreatedAtAction(nameof(GetUserById), new { id = user.Id }, user);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation during user creation");
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating user");
            return StatusCode(500, new { message = "An error occurred while creating the user" });
        }
    }

    [HttpPut("{id}")]
    [Authorize(Policy = "Users.Update")]
    public async Task<ActionResult<UserResponse>> UpdateUser(Guid id, [FromBody] UpdateUserRequest request)
    {
        try
        {
            var command = new UpdateUserCommand { UserId = id, Request = request };
            var user = await _mediator.Send(command);
            return Ok(user);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation during user update");
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating user");
            return StatusCode(500, new { message = "An error occurred while updating the user" });
        }
    }

    [HttpPatch("{id}/activate")]
    [Authorize(Policy = "Users.Update")]
    public async Task<ActionResult<UserResponse>> ActivateUser(Guid id)
    {
        try
        {
            var command = new UpdateUserCommand
            {
                UserId = id,
                Request = new UpdateUserRequest { IsActive = true }
            };
            var user = await _mediator.Send(command);
            return Ok(user);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation during user activation");
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error activating user");
            return StatusCode(500, new { message = "An error occurred while activating the user" });
        }
    }

    [HttpPatch("{id}/deactivate")]
    [Authorize(Policy = "Users.Update")]
    public async Task<ActionResult<UserResponse>> DeactivateUser(Guid id)
    {
        try
        {
            var command = new UpdateUserCommand
            {
                UserId = id,
                Request = new UpdateUserRequest { IsActive = false }
            };
            var user = await _mediator.Send(command);
            return Ok(user);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation during user deactivation");
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deactivating user");
            return StatusCode(500, new { message = "An error occurred while deactivating the user" });
        }
    }

    [HttpPost("{id}/roles")]
    //[Authorize(Policy = "Users.Update")]
    public async Task<ActionResult<UserResponse>> AssignRoles(Guid id, [FromBody] AssignRoleRequest request)
    {
        try
        {
            var command = new AssignRoleCommand { UserId = id, Request = request };
            var user = await _mediator.Send(command);
            return Ok(user);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation during role assignment");
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assigning roles");
            return StatusCode(500, new { message = "An error occurred while assigning roles" });
        }
    }
}
