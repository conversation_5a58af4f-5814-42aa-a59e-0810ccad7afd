{"format": 1, "restore": {"D:\\Projects\\VehicleManagementSystem\\VMS-API\\IdentityService\\src\\IdentityService.API\\IdentityService.API.csproj": {}}, "projects": {"D:\\Projects\\VehicleManagementSystem\\VMS-API\\IdentityService\\src\\IdentityService.API\\IdentityService.API.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Projects\\VehicleManagementSystem\\VMS-API\\IdentityService\\src\\IdentityService.API\\IdentityService.API.csproj", "projectName": "IdentityService.API", "projectPath": "D:\\Projects\\VehicleManagementSystem\\VMS-API\\IdentityService\\src\\IdentityService.API\\IdentityService.API.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Projects\\VehicleManagementSystem\\VMS-API\\IdentityService\\src\\IdentityService.API\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\Projects\\VehicleManagementSystem\\VMS-API\\IdentityService\\src\\IdentityService.Application\\IdentityService.Application.csproj": {"projectPath": "D:\\Projects\\VehicleManagementSystem\\VMS-API\\IdentityService\\src\\IdentityService.Application\\IdentityService.Application.csproj"}, "D:\\Projects\\VehicleManagementSystem\\VMS-API\\IdentityService\\src\\IdentityService.Infrastructure\\IdentityService.Infrastructure.csproj": {"projectPath": "D:\\Projects\\VehicleManagementSystem\\VMS-API\\IdentityService\\src\\IdentityService.Infrastructure\\IdentityService.Infrastructure.csproj"}, "D:\\Projects\\VehicleManagementSystem\\VMS-API\\VMSContracts\\VMSContracts.csproj": {"projectPath": "D:\\Projects\\VehicleManagementSystem\\VMS-API\\VMSContracts\\VMSContracts.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[12.0.1, )"}, "AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[12.0.1, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[8.0.2, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"target": "Package", "version": "[8.0.0, )"}, "Serilog": {"target": "Package", "version": "[4.2.0, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[8.0.0, )"}, "Serilog.Settings.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.4.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.201/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Projects\\VehicleManagementSystem\\VMS-API\\IdentityService\\src\\IdentityService.Application\\IdentityService.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Projects\\VehicleManagementSystem\\VMS-API\\IdentityService\\src\\IdentityService.Application\\IdentityService.Application.csproj", "projectName": "IdentityService.Application", "projectPath": "D:\\Projects\\VehicleManagementSystem\\VMS-API\\IdentityService\\src\\IdentityService.Application\\IdentityService.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Projects\\VehicleManagementSystem\\VMS-API\\IdentityService\\src\\IdentityService.Application\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\Projects\\VehicleManagementSystem\\VMS-API\\IdentityService\\src\\IdentityService.Domain\\IdentityService.Domain.csproj": {"projectPath": "D:\\Projects\\VehicleManagementSystem\\VMS-API\\IdentityService\\src\\IdentityService.Domain\\IdentityService.Domain.csproj"}, "D:\\Projects\\VehicleManagementSystem\\VMS-API\\VMSContracts\\VMSContracts.csproj": {"projectPath": "D:\\Projects\\VehicleManagementSystem\\VMS-API\\VMSContracts\\VMSContracts.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"FluentValidation": {"target": "Package", "version": "[11.11.0, )"}, "MediatR": {"target": "Package", "version": "[11.1.0, )"}, "MediatR.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[11.1.0, )"}, "Microsoft.AspNetCore.Http.Abstractions": {"target": "Package", "version": "[2.3.0, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.201/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Projects\\VehicleManagementSystem\\VMS-API\\IdentityService\\src\\IdentityService.Domain\\IdentityService.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Projects\\VehicleManagementSystem\\VMS-API\\IdentityService\\src\\IdentityService.Domain\\IdentityService.Domain.csproj", "projectName": "IdentityService.Domain", "projectPath": "D:\\Projects\\VehicleManagementSystem\\VMS-API\\IdentityService\\src\\IdentityService.Domain\\IdentityService.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Projects\\VehicleManagementSystem\\VMS-API\\IdentityService\\src\\IdentityService.Domain\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": {"target": "Package", "version": "[8.0.2, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.201/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Projects\\VehicleManagementSystem\\VMS-API\\IdentityService\\src\\IdentityService.Infrastructure\\IdentityService.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Projects\\VehicleManagementSystem\\VMS-API\\IdentityService\\src\\IdentityService.Infrastructure\\IdentityService.Infrastructure.csproj", "projectName": "IdentityService.Infrastructure", "projectPath": "D:\\Projects\\VehicleManagementSystem\\VMS-API\\IdentityService\\src\\IdentityService.Infrastructure\\IdentityService.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Projects\\VehicleManagementSystem\\VMS-API\\IdentityService\\src\\IdentityService.Infrastructure\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\Projects\\VehicleManagementSystem\\VMS-API\\IdentityService\\src\\IdentityService.Application\\IdentityService.Application.csproj": {"projectPath": "D:\\Projects\\VehicleManagementSystem\\VMS-API\\IdentityService\\src\\IdentityService.Application\\IdentityService.Application.csproj"}, "D:\\Projects\\VehicleManagementSystem\\VMS-API\\VMSContracts\\VMSContracts.csproj": {"projectPath": "D:\\Projects\\VehicleManagementSystem\\VMS-API\\VMSContracts\\VMSContracts.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"MassTransit": {"target": "Package", "version": "[8.1.3, )"}, "MassTransit.RabbitMQ": {"target": "Package", "version": "[8.1.3, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"target": "Package", "version": "[8.0.0, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.201/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Projects\\VehicleManagementSystem\\VMS-API\\VMSContracts\\VMSContracts.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Projects\\VehicleManagementSystem\\VMS-API\\VMSContracts\\VMSContracts.csproj", "projectName": "VMSContracts", "projectPath": "D:\\Projects\\VehicleManagementSystem\\VMS-API\\VMSContracts\\VMSContracts.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Projects\\VehicleManagementSystem\\VMS-API\\VMSContracts\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"MassTransit": {"target": "Package", "version": "[8.1.3, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Http.Polly": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.201/PortableRuntimeIdentifierGraph.json"}}}}}