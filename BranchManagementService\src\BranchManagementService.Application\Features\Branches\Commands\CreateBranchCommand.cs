using AutoMapper;
using BranchManagementService.Application.DTOs;
using BranchManagementService.Domain.Entities;
using BranchManagementService.Domain.Enums;
using BranchManagementService.Domain.Interfaces;
using MediatR;
using Microsoft.Extensions.Logging;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace BranchManagementService.Application.Features.Branches.Commands
{
    public class CreateBranchCommand : IRequest<BranchDto>
    {
        public Guid VendorId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string ContactPerson { get; set; } = string.Empty;
        public string MobileNumber { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
        public string City { get; set; } = string.Empty;
        public string State { get; set; } = string.Empty;
        public string ZipCode { get; set; } = string.Empty;
        public VehicleTypeSupport VehicleTypeSupport { get; set; }
    }

    public class CreateBranchCommandHandler : IRequestHandler<CreateBranchCommand, BranchDto>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly ILogger<CreateBranchCommandHandler> _logger;

        public CreateBranchCommandHandler(
            IUnitOfWork unitOfWork,
            IMapper mapper,
            ILogger<CreateBranchCommandHandler> logger)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<BranchDto> Handle(CreateBranchCommand request, CancellationToken cancellationToken)
        {
            try
            {
                // Check if branch limit is reached
                var branchCount = await _unitOfWork.Branches.GetBranchCountAsync(request.VendorId);
                
                // TODO: Get subscription limit from subscription service
                // For now, we'll use a default limit of 5
                int branchLimit = 5;
                
                if (branchCount >= branchLimit)
                {
                    throw new InvalidOperationException($"Branch limit of {branchLimit} has been reached for vendor {request.VendorId}");
                }

                // Create branch
                var branch = Branch.Create(
                    request.VendorId,
                    request.Name,
                    request.Email,
                    request.ContactPerson,
                    request.MobileNumber,
                    request.Address,
                    request.City,
                    request.State,
                    request.ZipCode,
                    request.VehicleTypeSupport,
                    "System");

                // Add branch to database
                await _unitOfWork.Branches.AddAsync(branch);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Branch {BranchId} created for vendor {VendorId}", branch.Id, request.VendorId);

                return _mapper.Map<BranchDto>(branch);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating branch for vendor {VendorId}", request.VendorId);
                throw;
            }
        }
    }
}
