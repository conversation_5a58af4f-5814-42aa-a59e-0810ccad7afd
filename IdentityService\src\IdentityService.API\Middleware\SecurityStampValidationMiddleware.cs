using System;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Threading.Tasks;
using IdentityService.Domain.Interfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace IdentityService.API.Middleware
{
    public class SecurityStampValidationMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<SecurityStampValidationMiddleware> _logger;

        public SecurityStampValidationMiddleware(RequestDelegate next, ILogger<SecurityStampValidationMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context, IUserRepository userRepository)
        {
            try
            {
                // Only check authenticated requests
                if (context.User.Identity?.IsAuthenticated == true)
                {
                    // Get the token from the Authorization header
                    var authHeader = context.Request.Headers["Authorization"].FirstOrDefault();
                    if (authHeader != null && authHeader.StartsWith("Bearer ", StringComparison.OrdinalIgnoreCase))
                    {
                        var token = authHeader.Substring("Bearer ".Length).Trim();
                        
                        // Parse the token
                        var tokenHandler = new JwtSecurityTokenHandler();
                        var jwtToken = tokenHandler.ReadJwtToken(token);
                        
                        // Get the user ID from the token
                        var userIdClaim = jwtToken.Claims.FirstOrDefault(c => c.Type == "sub" || c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier");
                        if (userIdClaim != null && Guid.TryParse(userIdClaim.Value, out var userId))
                        {
                            // Get the security stamp from the token
                            var securityStampClaim = jwtToken.Claims.FirstOrDefault(c => c.Type == "security_stamp");
                            if (securityStampClaim != null && long.TryParse(securityStampClaim.Value, out var tokenSecurityStampTicks))
                            {
                                // Get the user from the repository
                                var user = await userRepository.GetByIdAsync(userId);
                                if (user != null)
                                {
                                    // Compare the security stamps
                                    var tokenSecurityStamp = new DateTime(tokenSecurityStampTicks);
                                    if (tokenSecurityStamp < user.SecurityStamp)
                                    {
                                        _logger.LogWarning("Token security stamp is older than user security stamp. Token was issued before a security-critical change.");
                                        
                                        // Token was issued before a security-critical change, so it's invalid
                                        context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                                        await context.Response.WriteAsJsonAsync(new { message = "Your session has expired due to a security-critical change. Please log in again." });
                                        return;
                                    }
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating security stamp");
            }

            // Continue with the pipeline
            await _next(context);
        }
    }
}
