using AutoMapper;
using BranchManagementService.Application.DTOs;
using BranchManagementService.Domain.Interfaces;
using MediatR;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace BranchManagementService.Application.Features.Branches.Queries
{
    public class GetBranchesByVendorIdQuery : IRequest<List<BranchDto>>
    {
        public Guid VendorId { get; set; }
    }

    public class GetBranchesByVendorIdQueryHandler : IRequestHandler<GetBranchesByVendorIdQuery, List<BranchDto>>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly ILogger<GetBranchesByVendorIdQueryHandler> _logger;

        public GetBranchesByVendorIdQueryHandler(
            IUnitOfWork unitOfWork,
            IMapper mapper,
            ILogger<GetBranchesByVendorIdQueryHandler> logger)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<List<BranchDto>> Handle(GetBranchesByVendorIdQuery request, CancellationToken cancellationToken)
        {
            try
            {
                var branches = await _unitOfWork.Branches.GetByVendorIdAsync(request.VendorId);
                return _mapper.Map<List<BranchDto>>(branches);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting branches for vendor {VendorId}", request.VendorId);
                throw;
            }
        }
    }
}
