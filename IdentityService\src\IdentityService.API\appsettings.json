{"ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5432;Database=VMS_IdentityService;Username=postgres;Password=****;Include Error Detail=true"}, "Jwt": {"Key": "****567890****567890****56789012", "Issuer": "identity-service", "Audience": "vms-api", "ExpiryMinutes": 60, "RefreshTokenExpiryDays": 7}, "Email": {"SmtpServer": "smtp.gmail.com", "SmtpPort": 587, "SmtpUsername": "<EMAIL>", "SmtpPassword": "your-app-specific-password", "FromEmail": "<EMAIL>", "FromName": "VMS Identity Service"}, "RabbitMQ": {"HostName": "localhost", "UserName": "guest", "Password": "guest", "VirtualHost": "/"}, "PasswordComplexity": {"MinimumLength": 8, "RequireUppercase": true, "RequireLowercase": true, "RequireDigit": true, "RequireSpecialCharacter": true, "MaximumRepeatedCharacters": 3, "PreventCommonPasswords": true, "PreventUsernameInPassword": true}, "SessionManagement": {"IdleTimeoutMinutes": 30, "AbsoluteTimeoutHours": 24, "MaxConcurrentSessions": 5, "PreventConcurrentLogin": false}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}}, "ServiceUrls": {"TenantManagementService": "http://localhost:5003", "SubscriptionService": "http://localhost:5114"}, "AllowedHosts": "*"}