<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>
  <ItemGroup>
  <None Update="ocelot.SwaggerEndpoints.json">
    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
  </None>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.2"/>
    <PackageReference Include="Ocelot" Version="24.0.0"/>
    <PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.2"/>
    <PackageReference Include="MMLib.SwaggerForOcelot" Version="8.0.0"/>
  </ItemGroup>
</Project>