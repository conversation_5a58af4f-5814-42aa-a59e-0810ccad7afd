using System;
using System.Threading;
using System.Threading.Tasks;
using IdentityService.Application.Common;
using IdentityService.Application.DTOs.Auth;
using IdentityService.Application.Interfaces;
using IdentityService.Domain.Interfaces;
using MediatR;
using Microsoft.Extensions.Logging;

namespace IdentityService.Application.Features.Auth.Commands;

public class ChangePasswordCommand : BaseRequest<bool>
{
    public ChangePasswordRequest Request { get; set; }
}

public class ChangePasswordCommandHandler : IRequestHandler<ChangePasswordCommand, bool>
{
    private readonly IUserRepository _userRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<ChangePasswordCommandHandler> _logger;
    private readonly IPasswordHasher _passwordHasher;

    public ChangePasswordCommandHandler(
        IUserRepository userRepository,
        IUnitOfWork unitOfWork,
        ILogger<ChangePasswordCommandHandler> logger,
        IPasswordHasher passwordHasher)
    {
        _userRepository = userRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
        _passwordHasher = passwordHasher;
    }

    public async Task<bool> Handle(ChangePasswordCommand request, CancellationToken cancellationToken)
    {
        var user = await _userRepository.GetByIdAsync(request.UserId);
        if (user == null)
        {
            throw new ArgumentException("User not found");
        }

        if (!_passwordHasher.VerifyPassword(request.Request.CurrentPassword, user.PasswordHash.Hash))
        {
            throw new UnauthorizedAccessException("Current password is incorrect");
        }

        var newPasswordHash = _passwordHasher.HashPassword(request.Request.NewPassword);
        user.UpdatePassword(newPasswordHash, user.Id.ToString());

        await _unitOfWork.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Password changed successfully for user {UserId}", request.UserId);
        return true;
    }
}