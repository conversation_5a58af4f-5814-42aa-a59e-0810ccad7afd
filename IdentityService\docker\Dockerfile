FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["src/IdentityService.API/IdentityService.API.csproj", "IdentityService.API/"]
COPY ["src/IdentityService.Application/IdentityService.Application.csproj", "IdentityService.Application/"]
COPY ["src/IdentityService.Domain/IdentityService.Domain.csproj", "IdentityService.Domain/"]
COPY ["src/IdentityService.Infrastructure/IdentityService.Infrastructure.csproj", "IdentityService.Infrastructure/"]
COPY ["src/IdentityService.Shared/IdentityService.Shared.csproj", "IdentityService.Shared/"]
RUN dotnet restore "IdentityService.API/IdentityService.API.csproj"
COPY src/ .
WORKDIR "/src/IdentityService.API"
RUN dotnet build "IdentityService.API.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "IdentityService.API.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "IdentityService.API.dll"] 