using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using IdentityService.Application.Common;
using IdentityService.Application.DTOs.Menus;
using IdentityService.Domain.Interfaces;
using MediatR;

namespace IdentityService.Application.Features.Menus.Queries;

public class GetMenusQuery : BaseRequest<List<MenuResponse>>
{
}

public class GetMenusQueryHandler : IRequestHandler<GetMenusQuery, List<MenuResponse>>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetMenusQueryHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    public async Task<List<MenuResponse>> Handle(GetMenusQuery query, CancellationToken cancellationToken)
    {
        var menus = await _unitOfWork.MenuRepository.GetAllAsync();

        var menuResponses = new List<MenuResponse>();

        foreach (var menu in menus)
        {
            var menuResponse = new MenuResponse
            {
                Id = menu.Id,
                Name = menu.Name,
                DisplayName = menu.DisplayName,
                Path = menu.Path,
                Icon = menu.Icon,
                Order = menu.Order,
                ParentId = menu.ParentId,
                CreatedAt = menu.CreatedAt,
                CreatedBy = menu.CreatedBy,
                UpdatedAt = menu.UpdatedAt,
                UpdatedBy = menu.UpdatedBy,
                Permissions = new List<MenuPermissionResponse>()
            };

            // Add permissions
            foreach (var mp in menu.MenuPermissions)
            {
                if (mp.PermissionId.HasValue)
                {
                    var permission = await _unitOfWork.PermissionRepository.GetByIdAsync(mp.PermissionId.Value);
                    if (permission != null)
                    {
                        menuResponse.Permissions.Add(new MenuPermissionResponse
                        {
                            Id = permission.Id,
                            Name = permission.Name,
                            Description = permission.Description,
                            Resource = permission.Resource,
                            Action = permission.Action
                        });
                    }
                }
                else
                {
                    menuResponse.Permissions.Add(new MenuPermissionResponse
                    {
                        Id = System.Guid.Empty,
                        Name = "Public Access",
                        Description = "Accessible to all authenticated users",
                        Resource = "Menu",
                        Action = "View"
                    });
                }
            }

            menuResponses.Add(menuResponse);
        }

        return menuResponses.OrderBy(m => m.Order).ToList();
    }
}
