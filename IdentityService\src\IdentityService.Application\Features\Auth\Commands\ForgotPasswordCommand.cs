using System;
using System.Threading;
using System.Threading.Tasks;
using IdentityService.Application.Common;
using IdentityService.Application.DTOs.Auth;
using IdentityService.Application.Interfaces;
using IdentityService.Domain.Entities;
using IdentityService.Domain.Interfaces;
using MediatR;
using Microsoft.Extensions.Logging;

namespace IdentityService.Application.Features.Auth.Commands;

public class ForgotPasswordCommand : BaseRequest<bool>
{
    public ForgotPasswordRequest Request { get; set; }
}

public class ForgotPasswordCommandHandler : IRequestHandler<ForgotPasswordCommand, bool>
{
    private readonly IUserRepository _userRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<ForgotPasswordCommandHandler> _logger;
    private readonly IEmailService _emailService;

    public ForgotPasswordCommandHandler(
        IUserRepository userRepository,
        IUnitOfWork unitOfWork,
        ILogger<ForgotPasswordCommandHandler> logger,
        IEmailService emailService)
    {
        _userRepository = userRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
        _emailService = emailService;
    }

    public async Task<bool> Handle(ForgotPasswordCommand request, CancellationToken cancellationToken)
    {
        var user = await _userRepository.GetByEmailAsync(request.Request.Email);
        if (user == null)
        {
            // Return true to prevent email enumeration attacks
            return true;
        }

        var token = Guid.NewGuid().ToString("N");
        var expiryTime = DateTime.UtcNow.AddHours(24);

        var resetToken = PasswordResetToken.Create(token, expiryTime, user.Id, user, request.UserId.ToString() ?? "System");
        user.PasswordResetTokens.Add(resetToken);

        try
        {
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            var resetLink = $"https://your-app-url/reset-password?token={token}&email={user.Email}";
            await _emailService.SendPasswordResetEmailAsync(user.Email, resetLink);

            _logger.LogInformation("Password reset token generated for user {UserId}", user.Id);
            return true;
        }
        catch (InvalidOperationException ex) when (ex.Message.Contains("deleted by another user"))
        {
            // If there's a concurrency conflict, log it and return true
            // This prevents email enumeration attacks and provides a consistent response
            _logger.LogWarning(ex, "Concurrency conflict when generating password reset token for user {UserId}", user.Id);
            return true;
        }
    }
}