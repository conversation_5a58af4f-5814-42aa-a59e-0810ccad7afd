using System;
using System.Threading;
using System.Threading.Tasks;
using IdentityService.Application.Common;
using IdentityService.Application.DTOs.Auth;
using IdentityService.Application.Interfaces;
using IdentityService.Domain.Entities;
using IdentityService.Domain.Enums;
using IdentityService.Domain.Interfaces;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System.Linq;

namespace IdentityService.Application.Features.Auth.Commands;

public class LoginCommand : BaseRequest<LoginResponse>
{
    public required LoginRequest Request { get; set; }
}

public class LoginCommandHandler : IRequestHandler<LoginCommand, LoginResponse>
{
    private readonly IUserRepository _userRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<LoginCommandHandler> _logger;
    private readonly IPasswordHasher _passwordHasher;
    private readonly ITokenService _tokenService;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly ISessionManagementService _sessionManagementService;

    public LoginCommandHandler(
        IUserRepository userRepository,
        IUnitOfWork unitOfWork,
        ILogger<LoginCommandHandler> logger,
        IPasswordHasher passwordHasher,
        ITokenService tokenService,
        IHttpContextAccessor httpContextAccessor,
        ISessionManagementService sessionManagementService)
    {
        _userRepository = userRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
        _passwordHasher = passwordHasher;
        _tokenService = tokenService;
        _httpContextAccessor = httpContextAccessor;
        _sessionManagementService = sessionManagementService;
    }

    public async Task<LoginResponse> Handle(LoginCommand request, CancellationToken cancellationToken)
    {
        User user = null;
        string email = request.Request.Email;
        string phoneNumber = request.Request.PhoneNumber;
        LoginMethod loginMethod = LoginMethod.EmailPassword;
        string failureReason = null;

        try
        {
            if (request.Request.IsEmailLogin)
            {
                // Get user with roles and permissions included
                user = await _userRepository.GetByEmailAsync(email);
                if (user == null)
                {
                    failureReason = "User not found";
                    throw new UnauthorizedAccessException("Invalid email or password");
                }

                if (!_passwordHasher.VerifyPassword(request.Request.Password, user.PasswordHash.Hash))
                {
                    failureReason = "Invalid password";
                    throw new UnauthorizedAccessException("Invalid email or password");
                }

                // Ensure roles and permissions are loaded
                await LoadUserRolesAndPermissions(user);
                loginMethod = LoginMethod.EmailPassword;
            }
            else if (request.Request.IsPhoneLogin)
            {
                if (string.IsNullOrEmpty(phoneNumber))
                {
                    failureReason = "Phone number is required";
                    throw new ArgumentException("Phone number is required");
                }

                user = await _userRepository.GetByPhoneNumberAsync(phoneNumber);
                if (user == null)
                {
                    failureReason = "User not found";
                    throw new UnauthorizedAccessException("Invalid phone number or OTP");
                }

                if (string.IsNullOrEmpty(request.Request.Otp))
                {
                    failureReason = "OTP is required";
                    throw new ArgumentException("OTP is required");
                }

                if (!await ValidateOtpAsync(user.Id, request.Request.Otp))
                {
                    failureReason = "Invalid OTP";
                    throw new UnauthorizedAccessException("Invalid phone number or OTP");
                }

                loginMethod = LoginMethod.PhoneOtp;
            }
            else
            {
                failureReason = "Invalid login method";
                throw new ArgumentException("Invalid login request");
            }

            // Update user login information
            user.UpdateLastLogin();
            user.SetRememberMe(request.Request.RememberMe);

            // Generate tokens
            var (accessToken, refreshToken, accessTokenExpiry, refreshTokenExpiry) =
                await _tokenService.GenerateTokensAsync(user);

            user.SetRefreshToken(refreshToken, refreshTokenExpiry);

            // Get client information
            var httpContext = _httpContextAccessor.HttpContext;
            var ipAddress = httpContext?.Connection?.RemoteIpAddress?.ToString() ?? "Unknown";
            var userAgent = httpContext?.Request?.Headers["User-Agent"].ToString() ?? "Unknown";
            var deviceInfo = GetDeviceInfo(userAgent);
            var location = "Unknown"; // In a real app, you might use a geolocation service

            // Create login history entry (successful)
            var loginHistory = UserLoginHistory.CreateSuccessful(
                user.Id,
                user,
                email,
                phoneNumber,
                ipAddress,
                userAgent,
                deviceInfo,
                location,
                loginMethod,
                "System");

            await _unitOfWork.UserLoginHistoryRepository.AddAsync(loginHistory);

            // Create user session
            await _sessionManagementService.CreateSessionAsync(
                user.Id,
                accessToken,
                refreshToken,
                accessTokenExpiry,
                ipAddress,
                userAgent,
                deviceInfo);

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            var roles = await _userRepository.GetUserRolesAsync(user.Id);
            var permissions = await _userRepository.GetUserPermissionsAsync(user.Id);

            return new LoginResponse
            {
                AccessToken = accessToken,
                RefreshToken = refreshToken,
                AccessTokenExpiryTime = accessTokenExpiry,
                RefreshTokenExpiryTime = refreshTokenExpiry,
                User = new UserDto
                {
                    Id = user.Id,
                    Email = user.Email,
                    PhoneNumber = user.PhoneNumber,
                    LastLoginAt = user.LastLoginAt
                },
                Roles = roles.ToList(),
                Permissions = permissions.Select(p => p.Name).ToList()
            };
        }
        catch (Exception ex)
        {
            // Log the exception
            _logger.LogWarning(ex, "Login failed: {Message}", ex.Message);

            // Create login history entry (failed)
            try
            {
                var httpContext = _httpContextAccessor.HttpContext;
                var ipAddress = httpContext?.Connection?.RemoteIpAddress?.ToString() ?? "Unknown";
                var userAgent = httpContext?.Request?.Headers["User-Agent"].ToString() ?? "Unknown";
                var deviceInfo = GetDeviceInfo(userAgent);
                var location = "Unknown";

                var loginHistory = UserLoginHistory.CreateFailed(
                    user?.Id,
                    user,
                    email ?? "Unknown",
                    phoneNumber ?? "Unknown",
                    failureReason ?? ex.Message,
                    ipAddress,
                    userAgent,
                    deviceInfo,
                    location,
                    loginMethod,
                    "System");

                await _unitOfWork.UserLoginHistoryRepository.AddAsync(loginHistory);
                await _unitOfWork.SaveChangesAsync(cancellationToken);
            }
            catch (Exception logEx)
            {
                // Just log the exception, don't rethrow
                _logger.LogError(logEx, "Error logging failed login attempt");
            }

            // Rethrow the original exception
            throw;
        }
    }

    private string GetDeviceInfo(string userAgent)
    {
        // In a real app, you might use a library to parse the user agent
        // For now, we'll just return a simplified version
        if (string.IsNullOrEmpty(userAgent))
            return "Unknown";

        if (userAgent.Contains("Mobile") || userAgent.Contains("Android") || userAgent.Contains("iPhone"))
            return "Mobile";

        if (userAgent.Contains("Tablet") || userAgent.Contains("iPad"))
            return "Tablet";

        return "Desktop";
    }

    private Task<bool> ValidateOtpAsync(Guid userId, string otp)
    {
        // TODO: Implement OTP validation logic
        return Task.FromResult(true);
    }

    private Task LoadUserRolesAndPermissions(User user)
    {
        // We don't need to modify the user object directly
        // The repository methods GetUserRolesAsync and GetUserPermissionsAsync will be used
        // in the LoginCommandHandler.Handle method to get the roles and permissions
        // for the response and by the TokenService to generate the token

        // No action needed here as we've updated the repository methods to include
        // the necessary related data when loading the user
        return Task.CompletedTask;
    }
}