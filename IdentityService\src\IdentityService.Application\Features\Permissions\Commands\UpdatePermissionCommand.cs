using System;
using System.Threading;
using System.Threading.Tasks;
using IdentityService.Application.DTOs.Permissions;
using IdentityService.Application.Interfaces;
using IdentityService.Domain.Interfaces;
using MediatR;

namespace IdentityService.Application.Features.Permissions.Commands;

public class UpdatePermissionCommand : IRequest<PermissionResponse>
{
    public Guid PermissionId { get; set; }
    public UpdatePermissionRequest Request { get; set; }
}

public class UpdatePermissionCommandHandler : IRequestHandler<UpdatePermissionCommand, PermissionResponse>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICurrentUserService _currentUserService;
    private readonly IAuditLogService _auditLogService;

    public UpdatePermissionCommandHandler(
        IUnitOfWork unitOfWork,
        ICurrentUserService currentUserService,
        IAuditLogService auditLogService)
    {
        _unitOfWork = unitOfWork;
        _currentUserService = currentUserService;
        _auditLogService = auditLogService;
    }

    public async Task<PermissionResponse> Handle(UpdatePermissionCommand command, CancellationToken cancellationToken)
    {
        var permission = await _unitOfWork.PermissionRepository.GetByIdAsync(command.PermissionId);
        if (permission == null)
            throw new InvalidOperationException($"Permission with ID {command.PermissionId} not found");
            
        var request = command.Request;
        var updatedBy = _currentUserService.UserId.ToString() ?? "System";
        
        // Check if permission with same name already exists (excluding current permission)
        var permissions = await _unitOfWork.PermissionRepository.GetAllAsync();
        if (permissions.Any(p => p.Id != command.PermissionId && 
                               p.Name.Equals(request.Name, StringComparison.OrdinalIgnoreCase)))
            throw new InvalidOperationException($"Permission with name '{request.Name}' already exists");
        
        // Track old values for audit log
        var oldValues = System.Text.Json.JsonSerializer.Serialize(new { 
            permission.Name, 
            permission.Description, 
            permission.Resource, 
            permission.Action 
        });
        
        // Update permission
        permission.Update(
            request.Name,
            request.Description,
            request.Resource,
            request.Action,
            updatedBy);
        
        // Save changes
        await _unitOfWork.PermissionRepository.UpdateAsync(permission);
        await _unitOfWork.SaveChangesAsync(cancellationToken);
        
        // Track new values for audit log
        var newValues = System.Text.Json.JsonSerializer.Serialize(new { 
            permission.Name, 
            permission.Description, 
            permission.Resource, 
            permission.Action 
        });
        
        // Create audit log
        await _auditLogService.CreateAuditLogAsync(
            "Update",
            "Permission",
            permission.Id.ToString(),
            oldValues,
            newValues,
            "Name,Description,Resource,Action",
            _currentUserService.UserId ?? Guid.Empty);
        
        // Create response
        var response = new PermissionResponse
        {
            Id = permission.Id,
            Name = permission.Name,
            Description = permission.Description,
            Resource = permission.Resource,
            Action = permission.Action,
            CreatedAt = permission.CreatedAt,
            CreatedBy = permission.CreatedBy,
            UpdatedAt = permission.UpdatedAt,
            UpdatedBy = permission.UpdatedBy
        };
        
        return response;
    }
}
