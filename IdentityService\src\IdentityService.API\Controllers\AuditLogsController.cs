using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using IdentityService.Application.DTOs.AuditLogs;
using IdentityService.Application.Features.AuditLogs.Queries;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace IdentityService.API.Controllers;

[ApiController]
[Route("api/[controller]")]
//[Authorize(Policy = "AuditLogs.View")]
public class AuditLogsController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<AuditLogsController> _logger;

    public AuditLogsController(IMediator mediator, ILogger<AuditLogsController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    [HttpGet]
    public async Task<ActionResult<List<AuditLogResponse>>> GetAuditLogs([FromQuery] AuditLogFilterRequest filter)
    {
        try
        {
            var query = new GetAuditLogsQuery { Filter = filter };
            var (items, totalCount) = await _mediator.Send(query);
            
            // Add pagination headers
            Response.Headers.Add("X-Total-Count", totalCount.ToString());
            Response.Headers.Add("X-Page-Number", filter.PageNumber.ToString());
            Response.Headers.Add("X-Page-Size", filter.PageSize.ToString());
            Response.Headers.Add("X-Total-Pages", ((int)Math.Ceiling(totalCount / (double)filter.PageSize)).ToString());
            
            return Ok(items);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving audit logs");
            return StatusCode(500, new { message = "An error occurred while retrieving audit logs" });
        }
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<AuditLogResponse>> GetAuditLogById(Guid id)
    {
        try
        {
            var query = new GetAuditLogByIdQuery { AuditLogId = id };
            var auditLog = await _mediator.Send(query);
            return Ok(auditLog);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Audit log not found");
            return NotFound(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving audit log");
            return StatusCode(500, new { message = "An error occurred while retrieving the audit log" });
        }
    }

    [HttpGet("export")]
    public async Task<ActionResult> ExportAuditLogs([FromQuery] AuditLogFilterRequest filter, [FromQuery] string format = "csv")
    {
        try
        {
            var query = new ExportAuditLogsQuery { Filter = filter, ExportFormat = format };
            var fileBytes = await _mediator.Send(query);
            
            string contentType;
            string fileName;
            
            if (format.ToLower() == "pdf")
            {
                contentType = "application/pdf";
                fileName = $"audit_logs_{DateTime.Now:yyyyMMdd_HHmmss}.pdf";
            }
            else
            {
                contentType = "text/csv";
                fileName = $"audit_logs_{DateTime.Now:yyyyMMdd_HHmmss}.csv";
            }
            
            return File(fileBytes, contentType, fileName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting audit logs");
            return StatusCode(500, new { message = "An error occurred while exporting audit logs" });
        }
    }
}
