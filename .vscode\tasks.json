{"version": "2.0.0", "tasks": [{"label": "build", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/VMS.sln", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "build-identity", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/IdentityService/src/IdentityService.API/IdentityService.API.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "build-subscription", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/SubscriptionService/src/SubscriptionService.API/SubscriptionService.API.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "build-contracts", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/VMSContracts/VMSContracts.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "build-tenant", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/TenantManagementService/src/TenantManagementService.API/TenantManagementService.API.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "build-branch", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/BranchManagementService/src/BranchManagementService.API/BranchManagementService.API.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "build-vehicle", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/VehicleManagementService/src/VehicleManagementService.API/VehicleManagementService.API.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "publish", "command": "dotnet", "type": "process", "args": ["publish", "${workspaceFolder}/VMS.sln", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "watch", "command": "dotnet", "type": "process", "args": ["watch", "run", "--project", "${workspaceFolder}/VMS.sln"], "problemMatcher": "$msCompile"}]}