{"format": 1, "restore": {"D:\\Projects\\VehicleManagementSystem\\VMS-API\\ApiGateway\\ApiGateway.API\\ApiGateway.API.csproj": {}}, "projects": {"D:\\Projects\\VehicleManagementSystem\\VMS-API\\ApiGateway\\ApiGateway.API\\ApiGateway.API.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Projects\\VehicleManagementSystem\\VMS-API\\ApiGateway\\ApiGateway.API\\ApiGateway.API.csproj", "projectName": "ApiGateway.API", "projectPath": "D:\\Projects\\VehicleManagementSystem\\VMS-API\\ApiGateway\\ApiGateway.API\\ApiGateway.API.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Projects\\VehicleManagementSystem\\VMS-API\\ApiGateway\\ApiGateway.API\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"MMLib.SwaggerForOcelot": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[8.0.2, )"}, "Ocelot": {"target": "Package", "version": "[24.0.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[8.1.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.201/PortableRuntimeIdentifierGraph.json"}}}}}