using System.ComponentModel.DataAnnotations;

namespace IdentityService.Application.DTOs.Users;

public class CreateUserRequest
{
    [Required]
    [EmailAddress]
    public string Email { get; set; }

    [Required]
    [Phone]
    public string PhoneNumber { get; set; }

    [Required]
    [MinLength(8)]
    public string Password { get; set; }

    [Required]
    [Compare("Password")]
    public string ConfirmPassword { get; set; }

    public List<Guid>? RoleIds { get; set; }
    
    public bool IsActive { get; set; } = true;
}
