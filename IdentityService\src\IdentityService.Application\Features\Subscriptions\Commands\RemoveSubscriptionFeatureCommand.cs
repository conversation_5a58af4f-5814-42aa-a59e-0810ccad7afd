using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using IdentityService.Application.DTOs.Subscriptions;
using IdentityService.Application.Interfaces;
using IdentityService.Domain.Interfaces;
using MediatR;

namespace IdentityService.Application.Features.Subscriptions.Commands;

public class RemoveSubscriptionFeatureCommand : IRequest<UserSubscriptionResponse>
{
    public Guid SubscriptionId { get; set; }
    public string FeatureName { get; set; }
}

public class RemoveSubscriptionFeatureCommandHandler : IRequestHandler<RemoveSubscriptionFeatureCommand, UserSubscriptionResponse>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICurrentUserService _currentUserService;
    private readonly IAuditLogService _auditLogService;

    public RemoveSubscriptionFeatureCommandHandler(
        IUnitOfWork unitOfWork,
        ICurrentUserService currentUserService,
        IAuditLogService auditLogService)
    {
        _unitOfWork = unitOfWork;
        _currentUserService = currentUserService;
        _auditLogService = auditLogService;
    }

    public async Task<UserSubscriptionResponse> Handle(RemoveSubscriptionFeatureCommand command, CancellationToken cancellationToken)
    {
        var subscription = await _unitOfWork.UserSubscriptionRepository.GetBySubscriptionIdAsync(command.SubscriptionId);
        if (subscription == null)
            throw new InvalidOperationException($"Subscription with ID {command.SubscriptionId} not found");
            
        // Find feature
        var feature = subscription.Features.FirstOrDefault(f => 
            f.FeatureName.Equals(command.FeatureName, StringComparison.OrdinalIgnoreCase));
            
        if (feature == null)
            throw new InvalidOperationException($"Feature '{command.FeatureName}' not found for this subscription");
        
        // Track old values for audit log
        var oldValues = System.Text.Json.JsonSerializer.Serialize(new { 
            feature.Id,
            feature.FeatureName,
            feature.IsEnabled, 
            feature.UsageLimit 
        });
        
        // Remove feature
        subscription.RemoveFeature(command.FeatureName);
        
        // Save changes
        await _unitOfWork.UserSubscriptionRepository.UpdateAsync(subscription);
        await _unitOfWork.SaveChangesAsync(cancellationToken);
        
        // Create audit log
        await _auditLogService.CreateAuditLogAsync(
            "RemoveFeature",
            "UserSubscription",
            subscription.Id.ToString(),
            oldValues,
            string.Empty,
            "Id,FeatureName,IsEnabled,UsageLimit",
            _currentUserService.UserId ?? Guid.Empty);
        
        // Create response
        var response = new UserSubscriptionResponse
        {
            Id = subscription.Id,
            SubscriptionId = subscription.SubscriptionId,
            PlanName = subscription.PlanName,
            Tier = subscription.Tier,
            Status = subscription.Status,
            StartDate = subscription.StartDate,
            EndDate = subscription.EndDate,
            TrialEndDate = subscription.TrialEndDate,
            CreatedAt = subscription.CreatedAt,
            CreatedBy = subscription.CreatedBy,
            UpdatedAt = subscription.UpdatedAt,
            UpdatedBy = subscription.UpdatedBy,
            Features = subscription.Features.Select(f => new SubscriptionFeatureResponse
            {
                Id = f.Id,
                FeatureName = f.FeatureName,
                IsEnabled = f.IsEnabled,
                UsageLimit = f.UsageLimit
            }).ToList()
        };
        
        return response;
    }
}
