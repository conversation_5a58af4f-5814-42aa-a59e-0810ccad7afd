using System;
using System.Threading;
using System.Threading.Tasks;
using IdentityService.Application.Common;
using IdentityService.Application.DTOs.Roles;
using IdentityService.Application.Interfaces;
using IdentityService.Domain.Interfaces;
using MediatR;

namespace IdentityService.Application.Features.Roles.Commands;

public class AssignPermissionCommand : BaseRequest<RoleResponse>
{
    public Guid RoleId { get; set; }
    public AssignPermissionRequest Request { get; set; }
}

public class AssignPermissionCommandHandler : IRequestHandler<AssignPermissionCommand, RoleResponse>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICurrentUserService _currentUserService;
    private readonly IAuditLogService _auditLogService;

    public AssignPermissionCommandHandler(
        IUnitOfWork unitOfWork,
        ICurrentUserService currentUserService,
        IAuditLogService auditLogService)
    {
        _unitOfWork = unitOfWork;
        _currentUserService = currentUserService;
        _auditLogService = auditLogService;
    }

    public async Task<RoleResponse> Handle(AssignPermissionCommand command, CancellationToken cancellationToken)
    {
        var role = await _unitOfWork.RoleRepository.GetByIdAsync(command.RoleId);
        if (role == null)
            throw new InvalidOperationException($"Role with ID {command.RoleId} not found");

        var request = command.Request;
        var updatedBy = _currentUserService.UserId.ToString() ?? "System";

        // Track current permissions for audit log
        var currentPermissions = new List<object>();
        foreach (var rp in role.RolePermissions)
        {
            var permission = await _unitOfWork.PermissionRepository.GetByIdAsync(rp.PermissionId);
            if (permission != null)
            {
                currentPermissions.Add(new { permission.Id, permission.Name });
            }
        }
        var oldValues = System.Text.Json.JsonSerializer.Serialize(currentPermissions);

        // Remove existing permissions
        role.ClearPermissions();

        // Add new permissions
        foreach (var permissionId in request.PermissionIds)
        {
            var permission = await _unitOfWork.PermissionRepository.GetByIdAsync(permissionId);
            if (permission != null)
            {
                role.AddPermission(permission, updatedBy);
            }
        }

        // Save changes
        await _unitOfWork.RoleRepository.UpdateAsync(role);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        // Get updated permissions for audit log and response
        var updatedPermissions = new List<object>();
        var responsePermissions = new List<RolePermissionResponse>();
        foreach (var rp in role.RolePermissions)
        {
            var permission = await _unitOfWork.PermissionRepository.GetByIdAsync(rp.PermissionId);
            if (permission != null)
            {
                updatedPermissions.Add(new { permission.Id, permission.Name });
                responsePermissions.Add(new RolePermissionResponse
                {
                    Id = permission.Id,
                    Name = permission.Name,
                    Description = permission.Description,
                    Resource = permission.Resource,
                    Action = permission.Action
                });
            }
        }
        var newValues = System.Text.Json.JsonSerializer.Serialize(updatedPermissions);

        // Create audit log
        await _auditLogService.CreateAuditLogAsync(
            "AssignPermissions",
            "Role",
            role.Id.ToString(),
            oldValues,
            newValues,
            "RolePermissions",
            _currentUserService.UserId ?? Guid.Empty);

        // Create response
        var response = new RoleResponse
        {
            Id = role.Id,
            Name = role.Name,
            Description = role.Description,
            CreatedAt = role.CreatedAt,
            CreatedBy = role.CreatedBy,
            UpdatedAt = role.UpdatedAt,
            UpdatedBy = role.UpdatedBy,
            Permissions = responsePermissions
        };

        return response;
    }
}
