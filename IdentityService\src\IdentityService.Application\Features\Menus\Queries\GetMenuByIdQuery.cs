using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using IdentityService.Application.Common;
using IdentityService.Application.DTOs.Menus;
using IdentityService.Domain.Interfaces;
using MediatR;

namespace IdentityService.Application.Features.Menus.Queries;

public class GetMenuByIdQuery : BaseRequest<MenuResponse>
{
    public Guid MenuId { get; set; }
}

public class GetMenuByIdQueryHandler : IRequestHandler<GetMenuByIdQuery, MenuResponse>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetMenuByIdQueryHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    public async Task<MenuResponse> Handle(GetMenuByIdQuery query, CancellationToken cancellationToken)
    {
        var menu = await _unitOfWork.MenuRepository.GetByIdAsync(query.MenuId);
        if (menu == null)
            throw new InvalidOperationException($"Menu with ID {query.MenuId} not found");

        var menuResponse = new MenuResponse
        {
            Id = menu.Id,
            Name = menu.Name,
            DisplayName = menu.DisplayName,
            Path = menu.Path,
            Icon = menu.Icon,
            Order = menu.Order,
            ParentId = menu.ParentId,
            CreatedAt = menu.CreatedAt,
            CreatedBy = menu.CreatedBy,
            UpdatedAt = menu.UpdatedAt,
            UpdatedBy = menu.UpdatedBy,
            Permissions = new List<MenuPermissionResponse>()
        };

        // Add parent if exists
        if (menu.ParentId.HasValue)
        {
            var parentMenu = await _unitOfWork.MenuRepository.GetByIdAsync(menu.ParentId.Value);
            if (parentMenu != null)
            {
                menuResponse.Parent = new MenuResponse
                {
                    Id = parentMenu.Id,
                    Name = parentMenu.Name,
                    DisplayName = parentMenu.DisplayName,
                    Path = parentMenu.Path,
                    Icon = parentMenu.Icon,
                    Order = parentMenu.Order,
                    ParentId = parentMenu.ParentId,
                    CreatedAt = parentMenu.CreatedAt,
                    CreatedBy = parentMenu.CreatedBy,
                    UpdatedAt = parentMenu.UpdatedAt,
                    UpdatedBy = parentMenu.UpdatedBy
                };
            }
        }

        // Add permissions
        foreach (var mp in menu.MenuPermissions)
        {
            if (mp.PermissionId.HasValue)
            {
                var permission = await _unitOfWork.PermissionRepository.GetByIdAsync(mp.PermissionId.Value);
                if (permission != null)
                {
                    menuResponse.Permissions.Add(new MenuPermissionResponse
                    {
                        Id = permission.Id,
                        Name = permission.Name,
                        Description = permission.Description,
                        Resource = permission.Resource,
                        Action = permission.Action
                    });
                }
            }
            else
            {
                menuResponse.Permissions.Add(new MenuPermissionResponse
                {
                    Id = Guid.Empty,
                    Name = "Public Access",
                    Description = "Accessible to all authenticated users",
                    Resource = "Menu",
                    Action = "View"
                });
            }
        }

        // Add children
        var allMenus = await _unitOfWork.MenuRepository.GetAllAsync();
        foreach (var childMenu in allMenus.Where(m => m.ParentId == menu.Id))
        {
            menuResponse.Children.Add(new MenuResponse
            {
                Id = childMenu.Id,
                Name = childMenu.Name,
                DisplayName = childMenu.DisplayName,
                Path = childMenu.Path,
                Icon = childMenu.Icon,
                Order = childMenu.Order,
                ParentId = childMenu.ParentId,
                CreatedAt = childMenu.CreatedAt,
                CreatedBy = childMenu.CreatedBy,
                UpdatedAt = childMenu.UpdatedAt,
                UpdatedBy = childMenu.UpdatedBy
            });
        }

        return menuResponse;
    }
}
